package com.oplus.pay.channel.cn.wx

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.gson.Gson
import com.oplus.pay.basic.AppRuntime
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.basic.Resource
import com.oplus.pay.basic.util.serialize.GSON
import com.oplus.pay.basic.util.ui.ToastUtil
import com.oplus.pay.biz.TransType
import com.oplus.pay.channel.CHANNEL_SCENE_APP_PAY
import com.oplus.pay.channel.PAYMENT_TYPE_NORMAL_PAY
import com.oplus.pay.channel.PAYMENT_TYPE_PERIODIC_PAY
import com.oplus.pay.channel.StatResultId
import com.oplus.pay.channel.cn.statistic.StaticHelper
import com.oplus.pay.channel.cn.wx.ui.OldFashionedActivity
import com.oplus.pay.channel.model.CHANNEL_CANCEL
import com.oplus.pay.channel.model.CHANNEL_NOT_SUPPORT
import com.oplus.pay.channel.model.OpenChannelResult
import com.oplus.pay.channel.model.TRANS_MODE_OLD_FASHIONED
import com.oplus.pay.channel.model.request.OpenChannelParams
import com.oplus.pay.channel.provider.IChannelProvider
import com.oplus.pay.monitor.api.d
import com.oplus.pay.monitor.api.model.BizNode
import com.oplus.pay.monitor.api.model.BizResult
import com.oplus.pay.monitor.api.model.ChannelResultStatusCodes
import com.oplus.pay.monitor.api.model.ChannelRoutingStatusCodes
import com.platform.usercenter.trace.rumtime.AutoTrace
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelbiz.WXOpenBusinessWebview
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.modelpay.PayResp
import org.json.JSONObject
import java.util.*

import com.tencent.mm.opensdk.modelbiz.OpenWebview
import com.usercenter.custom.trace.WxChannelSceneTechTrace
import java.lang.ref.WeakReference


/**
 *
 * <p>Title: WxChannelHandler</p>
 * <p>Description: WxChannelHandler</p>
 * <p>Copyright (c) 2021 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR> by ******** on 2021/4/13.
 */
@Route(path = "/${PAYMENT_CODE_WX_PAY}/provider")
class WxOldNormalHandler : WxChannelHandler()
@Route(path = "/${PAYMENT_CODE_WX_PAY}/$PAYMENT_TYPE_NORMAL_PAY/provider")
class WxChannelNormalHandler : WxChannelHandler()
@Route(path = "/${PAYMENT_CODE_WX_PAY}/$PAYMENT_TYPE_NORMAL_PAY/$CHANNEL_CODE_WX_PAY/$CHANNEL_SCENE_APP_PAY/provider")
class WxChannelNormalWithChannelAndSceneHandler : WxChannelHandler()

@Route(path = "/${PAYMENT_CODE_WX_PAY}/$PAYMENT_TYPE_PERIODIC_PAY/provider")
class WxChannelPeriodicHandler : WxChannelHandler()
@Route(path = "/${PAYMENT_CODE_WX_PAY}/$PAYMENT_TYPE_PERIODIC_PAY/$CHANNEL_CODE_WX_PAY/$CHANNEL_SCENE_APP_PAY/provider")
class WxChannelPeriodicWithChannelAndSceneHandler : WxChannelHandler()

open class WxChannelHandler : IChannelProvider {
    private val wxPayFail = "open wxPay fail"
    companion object {
        private const val TAG = "WxChannelHandler"
        private val EXT_PAY_REQUEST_ID = "payRequestId"
        private val EXT_PAY_REQUEST_PACKAGE = "packageName"
        private val ERROR_OPEN_FAIL = "open wxPay fail"
        val CODE_UNKNOWN = -1
        val MSG_UNKNOWN = "unknown"
    }

    override fun isSupport(): Boolean {
        return WxPayHelper.isSupport()
    }

    private fun openChannelStart(openChannelParams: OpenChannelParams, resultId: String) {
        AutoTrace.get().upload(
            WxChannelSceneTechTrace.openChannelStart(
                countryCode = openChannelParams.bizExtra.countryCode,
                source = openChannelParams.bizExtra.source,
                order = openChannelParams.bizExtra.partnerOrder,
                token = openChannelParams.bizExtra.processToken,
                partnerId = openChannelParams.bizExtra.partnerCode,
                payType = openChannelParams.paymentCode,
                channelOrder = "",
                resultId = resultId
            )
        )

    }

    override fun pay(
        activity: Activity,
        openChannelParams: OpenChannelParams
    ): LiveData<Resource<OpenChannelResult>> {
        val buildCommonMap = StaticHelper.buildCommonMap(
            processToken = openChannelParams.bizExt.processToken,
            order = openChannelParams.bizExt.partnerOrder,
            prePayToken = openChannelParams.bizExt.prePayToken ?: "",
            bizNode = BizNode.CHANNEL_ROUTING.value,
            payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
            bizResult = BizResult.SUCCESS.value,
            bizCode = ChannelRoutingStatusCodes.CODE_03_000_0000.statusCode,
            bizErrorMsg = "",
            routingMethod = "pay",
            payType = StaticHelper.getPayType(openChannelParams),
            outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                put("partnerCode",openChannelParams.bizExt.partnerCode)
                put("channelCode",openChannelParams.channelCode)
                put("transType",openChannelParams.channelExtras.transType ?: "")
                put("transMode",openChannelParams.channelExtras.transMode ?: "")
                put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
            })
        )
        PayLogUtil.d(TAG, buildCommonMap)
        StaticHelper.eventIdChannelRoutingStart(buildCommonMap)

        val result = MutableLiveData<Resource<OpenChannelResult>>()
        if (isSupport()) {
            val requestMsg: String = openChannelParams.channelOrder.channelData
            if (openChannelParams.channelExtras.transType != null
                && TransType.SIGN.type == openChannelParams.channelExtras.transType
            ) {
                if (TRANS_MODE_OLD_FASHIONED == openChannelParams.channelExtras.transMode) {
                    // 旧的云服务签约并支付
                    startOldFashionedSign(activity, requestMsg, openChannelParams, result)
                } else {
                    // 纯签约的订单
                    startAppSign(activity, requestMsg, openChannelParams, result)
                }
            } else {
                if(openChannelParams.channelExtras.serverTradeType==TransType.SIGN.type){//消费签约 0元纯签约
                    // 纯签约的订单
                    startAppSign(activity, requestMsg, openChannelParams, result)
                }else{
                    // 正常支付/签约并支付的订单
                    startNormalPay(activity, requestMsg, openChannelParams, result)
                }
            }
        } else {
            val buildCommonMap = StaticHelper.buildCommonMap(
                processToken = openChannelParams.bizExt.processToken,
                order = openChannelParams.bizExt.partnerOrder,
                prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                bizNode = BizNode.CHANNEL_ROUTING.value,
                payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                bizResult = BizResult.ERROR.value,
                bizCode = ChannelRoutingStatusCodes.CODE_03_000_0007.statusCode,
                bizErrorMsg = "wx not support",
                routingMethod = "pay",
                payType = StaticHelper.getPayType(openChannelParams),
                outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                    put("partnerCode",openChannelParams.bizExt.partnerCode)
                    put("channelCode",openChannelParams.channelCode)
                })
            )
            PayLogUtil.d(TAG, buildCommonMap)
            StaticHelper.eventIdChannelRoutingEnd(buildCommonMap)

            openChannelStart(openChannelParams, StatResultId.FAIL.value)
            ToastUtil.show(R.string.install_new_wechat)
            notifyError(CHANNEL_NOT_SUPPORT, "weChat pay not support", result)
        }
        return result
    }

    /**
     * 纯签约 -- APP签约
     */
    private fun startAppSign(
        activity: Activity,
        requestMsg: String,
        openChannelParams: OpenChannelParams,
        result: MutableLiveData<Resource<OpenChannelResult>>
    ) {
        val req = WXOpenBusinessWebview.Req()
        req.businessType = 12 //固定值
        val queryInfo = HashMap<String, String>()
        queryInfo["pre_entrustweb_id"] = requestMsg
        req.queryInfo = queryInfo

        openWxApi(req, result, openChannelParams)

        LocalBroadcastManager.getInstance(activity).registerReceiver(
            SignReceiver(this, openChannelParams, result, WeakReference(activity)),
            IntentFilter().apply {
                addAction(ACTION_WECHAT_SIGN)
            })
    }

    /**
     * 纯签约广播通知
     */
    private class SignReceiver(
        private val handler: WxChannelHandler,
        private val openChannelParams: OpenChannelParams,
        private val result: MutableLiveData<Resource<OpenChannelResult>>,
        private val ref: WeakReference<Activity>
    ) : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val hasExtra = intent?.hasExtra(EXTRA_WECHAT_SIGN)
            val baseResp = intent?.getStringExtra(EXTRA_WECHAT_BASE_RESP)
            if (hasExtra == true) {
                val signSuccess = intent.getBooleanExtra(EXTRA_WECHAT_SIGN, false)
                if (signSuccess) {
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = openChannelParams.bizExt.processToken,
                        order = openChannelParams.bizExt.partnerOrder,
                        prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                        bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                        payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                        bizResult = BizResult.SUCCESS.value,
                        bizCode = ChannelResultStatusCodes.CODE_04_000_0000.statusCode,
                        bizErrorMsg = "",
                        payType = StaticHelper.getPayType(openChannelParams),
                        outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                            put("partnerCode",openChannelParams.bizExt.partnerCode)
                            put("channelCode",openChannelParams.channelCode)
                            put("transType",openChannelParams.channelExtras.transType ?: "")
                            put("transMode",openChannelParams.channelExtras.transMode ?: "")
                            put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                            put("baseResp",baseResp ?: "")
                        })
                    )
                    PayLogUtil.d(TAG, buildCommonMap)
                    StaticHelper.eventIdChannelPayEnd(buildCommonMap)

                    handler.notifySuccess(
                        OpenChannelResult(
                            channelId = PAYMENT_CODE_WX_PAY,
                            payOrder = openChannelParams.payReqId,
                            contractCode = openChannelParams.bizExtra.contractCode
                        ), result
                    )
                } else {
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = openChannelParams.bizExt.processToken,
                        order = openChannelParams.bizExt.partnerOrder,
                        prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                        bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                        payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                        bizResult = BizResult.WARN.value,
                        bizCode = ChannelResultStatusCodes.CODE_04_000_0002.statusCode,
                        bizErrorMsg = "",
                        payType = StaticHelper.getPayType(openChannelParams),
                        outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                            put("partnerCode",openChannelParams.bizExt.partnerCode)
                            put("channelCode",openChannelParams.channelCode)
                            put("transType",openChannelParams.channelExtras.transType ?: "")
                            put("transMode",openChannelParams.channelExtras.transMode ?: "")
                            put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                            put("baseResp",baseResp ?: "")
                        })
                    )
                    PayLogUtil.d(TAG, buildCommonMap)
                    StaticHelper.eventIdChannelPayEnd(buildCommonMap)

                    handler.notifyError(CODE_UNKNOWN, MSG_UNKNOWN, result)
                }
                ref.get()?.let { LocalBroadcastManager.getInstance(it).unregisterReceiver(this) }
            }
        }
    }

    /**
     * 老的云服务签约并支付，微信无回调，需要主动查询
     */
    private fun startOldFashionedSign(
        activity: Activity,
        requestMsg: String,
        openChannelParams: OpenChannelParams,
        result: MutableLiveData<Resource<OpenChannelResult>>
    ) {
        // 中转页面，用于查询签约结果
        OldFashionedActivity.launch(activity, openChannelParams)

        val req = OpenWebview.Req()
        req.url = requestMsg
        openWxApi(req, result, openChannelParams)
    }

    /**
     * 正常支付 签约并支付的订单
     */
    private fun startNormalPay(
        activity: Activity,
        requestMsg: String,
        openChannelParams: OpenChannelParams,
        result: MutableLiveData<Resource<OpenChannelResult>>
    ) {
        val mWxParam = WxpayParam(requestMsg)
        val req: PayReq =
            getPayReq(mWxParam, openChannelParams.payReqId, openChannelParams.appPackage)
        openWxApi(req, result, openChannelParams)

        LocalBroadcastManager.getInstance(activity).registerReceiver(
            NormalPayReceiver(this, openChannelParams, result, WeakReference(activity)),
            IntentFilter().apply {
                addAction(ACTION_WECHAT_PAY)
            })
    }

    /**
     * 正常支付 签约并支付的订单广播
     */
    private class NormalPayReceiver(
        private val handler: WxChannelHandler,
        private val openChannelParams: OpenChannelParams,
        private val result: MutableLiveData<Resource<OpenChannelResult>>,
        private val ref: WeakReference<Activity>

    ) : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.hasExtra(EXTRA_WECHAT_PAY) == true) {
                val bundle = intent.getBundleExtra(EXTRA_WECHAT_PAY)
                val baseResp = intent?.getStringExtra(EXTRA_WECHAT_BASE_RESP)
                bundle?.takeIf { it.size() != 0 }.apply {
                    val payResp = PayResp(bundle)
                    payResp.extData?.let {
                        try {
                            val extData = JSONObject(it)
                            val payRequestId = extData.optString(EXT_PAY_REQUEST_ID)
                            val appPackage = extData.optString(EXT_PAY_REQUEST_PACKAGE)

                            if (openChannelParams.appPackage == appPackage
                                && openChannelParams.payReqId == payRequestId
                            ) {
                                ref.get()?.apply {
                                    LocalBroadcastManager.getInstance(this)
                                        .unregisterReceiver(this@NormalPayReceiver)
                                }
                                // 支付成功
                                handler.handleResponse(
                                    openChannelParams.payReqId,
                                    payResp,
                                    result,
                                    openChannelParams,
                                    baseResp
                                )
                            }
                        } catch (e: Exception) {
                            PayLogUtil.e(e.localizedMessage)
                            var buildCommonMap = StaticHelper.buildCommonMap(
                                processToken = openChannelParams.bizExt.processToken,
                                order = openChannelParams.bizExt.partnerOrder,
                                prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                                bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                                payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                                bizResult = BizResult.ERROR.value,
                                bizErrorMsg = "${e.message}",
                                bizCode = ChannelResultStatusCodes.CODE_04_000_0007.statusCode,
                                payType = StaticHelper.getPayType(openChannelParams),
                                outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                                    put("partnerCode",openChannelParams.bizExt.partnerCode)
                                    put("channelCode",openChannelParams.channelCode)
                                    put("transType",openChannelParams.channelExtras.transType ?: "")
                                    put("transMode",openChannelParams.channelExtras.transMode ?: "")
                                    put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                                    put("baseResp",baseResp ?: "")
                                })
                            )
                            PayLogUtil.d(TAG, buildCommonMap)
                            StaticHelper.eventIdChannelPayEnd(buildCommonMap)
                        }
                    }
                } ?: handler.handleResponse(
                    openChannelParams.payReqId,
                    null,
                    result,
                    openChannelParams,
                    baseResp
                )

            }
        }
    }

    /**
     * 打开微信支付api
     */
    private fun openWxApi(
        req: BaseReq,
        result: MutableLiveData<Resource<OpenChannelResult>>,
        openChannelParams: OpenChannelParams
    ) {
        PayLogUtil.i(TAG, "open weChat pay")
        val buildCommonMap = StaticHelper.buildCommonMap(
            processToken = openChannelParams.bizExt.processToken,
            order = openChannelParams.bizExt.partnerOrder,
            prePayToken = openChannelParams.bizExt.prePayToken ?: "",
            bizNode = BizNode.CHANNEL_CONFIRMATION.value,
            payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
            bizResult = BizResult.SUCCESS.value,
            bizCode = ChannelResultStatusCodes.CODE_04_000_0000.statusCode,
            bizErrorMsg = "",
            payType = StaticHelper.getPayType(openChannelParams),
            outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                put("partnerCode",openChannelParams.bizExt.partnerCode)
                put("channelCode",openChannelParams.channelCode)
                put("transType",openChannelParams.channelExtras.transType ?: "")
                put("transMode",openChannelParams.channelExtras.transMode ?: "")
                put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
            })
        )
        PayLogUtil.d(TAG, buildCommonMap)
        StaticHelper.eventIdChannelPayStart(buildCommonMap)
        try {
            if (!WxPayHelper.getOpenApi(AppRuntime.getAppContext()).sendReq(req)) {
                notifyError(CODE_UNKNOWN, ERROR_OPEN_FAIL, result)
                openChannelStart(openChannelParams, StatResultId.FAIL.value)
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = openChannelParams.bizExt.processToken,
                    order = openChannelParams.bizExt.partnerOrder,
                    prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                    bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                    payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                    bizResult = BizResult.ERROR.value,
                    bizErrorMsg = "${CODE_UNKNOWN}--${ERROR_OPEN_FAIL}",
                    bizCode = ChannelResultStatusCodes.CODE_04_000_0001.statusCode,
                    payType = StaticHelper.getPayType(openChannelParams),
                    outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                        put("partnerCode",openChannelParams.bizExt.partnerCode)
                        put("channelCode",openChannelParams.channelCode)
                        put("transType",openChannelParams.channelExtras.transType ?: "")
                        put("transMode",openChannelParams.channelExtras.transMode ?: "")
                        put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdChannelPayEnd(buildCommonMap)
            } else {
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = openChannelParams.bizExt.processToken,
                    order = openChannelParams.bizExt.partnerOrder,
                    prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                    bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                    payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                    bizResult = BizResult.SUCCESS.value,
                    bizCode = ChannelResultStatusCodes.CODE_04_000_0000.statusCode,
                    bizErrorMsg = "",
                    payType = StaticHelper.getPayType(openChannelParams),
                    outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                        put("partnerCode",openChannelParams.bizExt.partnerCode)
                        put("channelCode",openChannelParams.channelCode)
                        put("transType",openChannelParams.channelExtras.transType ?: "")
                        put("transMode",openChannelParams.channelExtras.transMode ?: "")
                        put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdChannelPayEnd(buildCommonMap)
                openChannelStart(openChannelParams, StatResultId.SUCCESS.value)
            }
        } catch (e: Exception) {
            val buildCommonMap = StaticHelper.buildCommonMap(
                processToken = openChannelParams.bizExt.processToken,
                order = openChannelParams.bizExt.partnerOrder,
                prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                bizResult = BizResult.ERROR.value,
                bizErrorMsg = "${CODE_UNKNOWN}--${e.message}",
                bizCode = ChannelResultStatusCodes.CODE_04_000_0001.statusCode,
                payType = StaticHelper.getPayType(openChannelParams),
                outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                    put("partnerCode",openChannelParams.bizExt.partnerCode)
                    put("channelCode",openChannelParams.channelCode)
                    put("transType",openChannelParams.channelExtras.transType ?: "")
                    put("transMode",openChannelParams.channelExtras.transMode ?: "")
                    put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                })
            )
            PayLogUtil.d(TAG, buildCommonMap)
            StaticHelper.eventIdChannelPayEnd(buildCommonMap)
            notifyError(CODE_UNKNOWN, ERROR_OPEN_FAIL, result)
            openChannelStart(openChannelParams, StatResultId.FAIL.value)
        }
    }

    override fun init(context: Context?) {
        // nothing
    }

    /**
     * 获取微信支付请求的参数
     */
    private fun getPayReq(wxParam: WxpayParam, payRequestId: String, appPackage: String): PayReq {
        val req = PayReq()
        req.appId = wxParam.appid
        req.partnerId = wxParam.partnerid
        req.prepayId = wxParam.prepayid
        req.packageValue = wxParam.packageNm
        req.nonceStr = wxParam.noncestr
        req.timeStamp = wxParam.timestamp
        req.sign = wxParam.sign

        val packageName: String =
            if (!TextUtils.isEmpty(appPackage)) appPackage else wxParam.packageNm
        req.extData = getWeChatExtData(payRequestId, packageName)
        PayLogUtil.i(TAG, "prepayId=" + req.prepayId + ",extData=" + req.extData)
        return req
    }

    /**
     * 附加参数
     */
    private fun getWeChatExtData(payRequestId: String?, packageName: String?): String? {
        val map: HashMap<String?, String?> = HashMap()
        if (!TextUtils.isEmpty(payRequestId)) {
            map[EXT_PAY_REQUEST_ID] = payRequestId
        }
        if (!TextUtils.isEmpty(packageName)) {
            map[EXT_PAY_REQUEST_PACKAGE] = packageName
        }
        return Gson().toJson(map)
    }

    /**
     * 处理微信回调逻辑
     */
    fun handleResponse(
        payRequestId: String,
        response: BaseResp?,
        result: MutableLiveData<Resource<OpenChannelResult>>,
        openChannelParams: OpenChannelParams,
        baseResp: String?
    ) {
        response?.apply {
            var bizResult = BizResult.WARN.value
            var bizCode = ChannelResultStatusCodes.CODE_04_000_0002.statusCode
            var errStr = response.errStr
            when (response.errCode) {
                BaseResp.ErrCode.ERR_OK -> {
                    bizResult = BizResult.SUCCESS.value
                    bizCode = ChannelResultStatusCodes.CODE_04_000_0000.statusCode
                    notifySuccess(OpenChannelResult(PAYMENT_CODE_WX_PAY, payRequestId), result)
                }
                BaseResp.ErrCode.ERR_USER_CANCEL -> {
                    bizCode = ChannelResultStatusCodes.CODE_04_000_0003.statusCode

                    notifyError(CHANNEL_CANCEL, "cancel" + System.currentTimeMillis(), result)
                }
                BaseResp.ErrCode.ERR_AUTH_DENIED -> {
                    if (TextUtils.isEmpty(errStr)) {
                        errStr =
                            AppRuntime.getAppContext().getString(R.string.wechat_premission_failed)
                    }
                    notifyError(errCode, errStr, result)
                }
                BaseResp.ErrCode.ERR_COMM -> {
                    if (TextUtils.isEmpty(errStr)) {
                        errStr = AppRuntime.getAppContext().getString(com.oplus.pay.ui.R.string.unknow_error)
                    }
                    notifyError(errCode, errStr, result)
                }
                BaseResp.ErrCode.ERR_SENT_FAILED -> {
                    if (TextUtils.isEmpty(errStr)) {
                        errStr = AppRuntime.getAppContext().getString(R.string.wechat_failed)
                    }
                    notifyError(errCode, errStr, result)
                }
                BaseResp.ErrCode.ERR_UNSUPPORT -> {
                    if (TextUtils.isEmpty(errStr)) {
                        errStr =
                            AppRuntime.getAppContext().getString(R.string.no_support_wechat_version)
                    }
                    notifyError(errCode, errStr, result)
                }
                else -> {
                    errStr = AppRuntime.getAppContext().getString(R.string.wechat_unknow_error)
                    notifyError(errCode, errStr, result)
                }
            }
            var buildCommonMap = StaticHelper.buildCommonMap(
                processToken = openChannelParams.bizExt.processToken,
                order = openChannelParams.bizExt.partnerOrder,
                prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                bizResult = bizResult,
                bizErrorMsg = "${errCode}-$errStr",
                bizCode = bizCode,
                payType = StaticHelper.getPayType(openChannelParams),
                outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                    put("partnerCode",openChannelParams.bizExt.partnerCode)
                    put("channelCode",openChannelParams.channelCode)
                    put("transType",openChannelParams.channelExtras.transType ?: "")
                    put("transMode",openChannelParams.channelExtras.transMode ?: "")
                    put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                    put("baseResp",baseResp ?: "")
                })
            )
            PayLogUtil.d(TAG, buildCommonMap)
            StaticHelper.eventIdChannelPayEnd(buildCommonMap)
        } ?: kotlin.run {
            val buildCommonMap = StaticHelper.buildCommonMap(
                processToken = openChannelParams.bizExt.processToken,
                order = openChannelParams.bizExt.partnerOrder,
                prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                bizResult = BizResult.ERROR.value,
                bizCode = ChannelResultStatusCodes.CODE_04_000_0007.statusCode,
                bizErrorMsg = "response is null",
                payType = StaticHelper.getPayType(openChannelParams),
                outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                    put("partnerCode",openChannelParams.bizExt.partnerCode)
                    put("channelCode",openChannelParams.channelCode)
                    put("transType",openChannelParams.channelExtras.transType ?: "")
                    put("transMode",openChannelParams.channelExtras.transMode ?: "")
                    put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                    put("baseResp",baseResp ?: "")
                })
            )
            PayLogUtil.d(TAG, buildCommonMap)
            StaticHelper.eventIdChannelPayEnd(buildCommonMap)
            notifyError(CODE_UNKNOWN, MSG_UNKNOWN, result)
        }
    }

    private fun notifyError(
        code: Int,
        msg: String,
        result: MutableLiveData<Resource<OpenChannelResult>>
    ) {
        result.value = Resource.error(code, msg)
    }

    private fun notifySuccess(
        openChannelResult: OpenChannelResult,
        result: MutableLiveData<Resource<OpenChannelResult>>
    ) {
        result.value = Resource.success(openChannelResult)
    }

}