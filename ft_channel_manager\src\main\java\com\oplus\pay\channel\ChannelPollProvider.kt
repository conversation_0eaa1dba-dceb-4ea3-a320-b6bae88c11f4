package com.oplus.pay.channel

import androidx.fragment.app.FragmentActivity
import com.alibaba.android.arouter.facade.annotation.Route
import com.oplus.pay.channel.api.ChannelRouter
import com.oplus.pay.channel.model.request.OpenChannelParams
import com.oplus.pay.channel.poll.PollingTaskObserver
import com.oplus.pay.channel.poll.QueryChannelResultObserver
import com.oplus.pay.channel.poll.QueryPayResultObserver
import com.oplus.pay.channel.polling.IChannelPollPayResult
import com.oplus.pay.channel.polling.IChannelPollingTaskObserver
import com.oplus.pay.channel.polling.IChannelQueryPayResult
import com.oplus.pay.channel.provider.IChannelPollProvider
import java.lang.ref.SoftReference

/**
 * <p>Title: ChannelPollProvider</p>
 * <p>Description: 渠道反查</p>
 * <p>Copyright (c) 2023-2023/9/15 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR>
 * @version 1.0
 *
 */
@Route(path = ChannelRouter.CHANNEL_POLL_PROVIDER)
class ChannelPollProvider : IChannelPollProvider {

    override fun initChannelPollObserver(
        activityRef: SoftReference<FragmentActivity>,
        payType: String,
        token: String,
        pollingDialogCancelAble: Boolean,
        isNeedConfirmDialog: Boolean,
    ): IChannelPollPayResult {
        return QueryPayResultObserver(
            activityRef = activityRef,
            payType = payType,
            token = token,
            pollingDialogCancelAble = pollingDialogCancelAble,
            isNeedConfirmDialog = isNeedConfirmDialog
        )
    }

    override fun initChannelPollingTask(
        activityRef: SoftReference<FragmentActivity>,
        payType: String,
        token: String,
        task: Runnable
    ): IChannelPollingTaskObserver {
        return PollingTaskObserver(
            activityRef = activityRef,
            payType = payType,
            token = token,
            task = task
        )
    }

    override fun initChannelQueryPayObserver(
        activityRef: SoftReference<FragmentActivity>,
        payType: String
    ): IChannelQueryPayResult {
        return QueryChannelResultObserver(
            activityRef = activityRef,
            payType = payType
        )
    }
}