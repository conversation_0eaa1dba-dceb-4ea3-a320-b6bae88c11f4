package com.oplus.pay.trade.utils

import android.app.Activity
import android.os.Handler
import android.text.TextUtils
import androidx.activity.ComponentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import com.oplus.pay.basic.AppRuntime
import com.oplus.pay.subscription.AssetsHelper
import com.oplus.pay.subscription.model.response.Voucher
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.basic.Resource
import com.oplus.pay.basic.Status
import com.oplus.pay.basic.util.serialize.toJson
import com.oplus.pay.basic.util.ui.ToastUtil
import com.oplus.pay.biz.OrderType
import com.oplus.pay.biz.TransType
import com.oplus.pay.channel.ChannelHelper
import com.oplus.pay.channel.model.response.V2Channel
import com.oplus.pay.marketing.model.response.CombineOrderInfo
import com.oplus.pay.monitor.api.e
import com.oplus.pay.monitor.api.model.BizKeyType
import com.oplus.pay.monitor.api.model.BizNode
import com.oplus.pay.monitor.api.model.BizResult
import com.oplus.pay.monitor.api.model.TransactionConfirmationStatusCodes
import com.oplus.pay.monitor.api.w
import com.oplus.pay.order.PayCenterHelper
import com.oplus.pay.order.api.PayCenterRouter
import com.oplus.pay.order.model.request.*
import com.oplus.pay.safe.model.FingerPrintCode
import com.oplus.pay.subscription.model.response.V2Assets
import com.oplus.pay.trade.R
import com.oplus.pay.trade.model.PayRequest
import com.oplus.pay.trade.ui.DirectPayActivity
import com.oplus.pay.trade.usecase.PayActionUseCase
import com.oplus.pay.trade.usecase.TradeCenterStaticUseCase
import com.oplus.pay.trade.viewmodel.QuickPayConfigCacheViewModel
import com.oplus.pay.ui.util.LoadingDialogHelper
import com.oplus.pay.ui.util.TipsHelper
import com.platform.usercenter.trace.rumtime.AutoTrace
import com.usercenter.custom.trace.TradeCenterSceneTrace
import org.json.JSONObject
import java.math.BigDecimal

class PayHelper(
    val mPayRequest: PayRequest,
    val activity: Activity,
    private val viewLifecycleOwner: LifecycleOwner,
    val screenType: String,
    val attachCheckBox: Boolean,
    val currentChannel: V2Channel?,
    val currentVoucher: Voucher?,
    val combineOrderInfo: CombineOrderInfo?,
    val actualAmount: String,
    val voucherUseAmount: Int,
    val actualCreditAmount: Int,
    val isEnough: Boolean,
    val creditSwitchBoxStatus: Boolean,
    val isVoucherBuyPlace: Boolean,
    val assets: V2Assets?,
    val currentCoin: Float?,
    val currentCredit: Float?,
    val payRequestId: MutableLiveData<String>,
    val contractCode: MutableLiveData<String>,
    val defaultPayType: String? = null,
    val isPayLoading: MutableLiveData<Boolean>?=null,
) {
    fun dispatchPay(ticketNo: String?, ticketValidateType: String? = null): String {
        LoadingDialogHelper.showPayLoadingDialog(isPayLoading)
        return if (mPayRequest.isAutoRenewToPayCenter) {
            if (OrderType.SIGN.ori == mPayRequest.mAutoRenew) {//签约
                if (TextUtils.isEmpty(mPayRequest.prePayToken)) {
                    goSignAndPay(OrderType.SIGN, ticketNo, ticketValidateType)
                } else {
                    goPay(OrderType.SIGN, ticketNo, ticketValidateType)
                }
            } else {
                if (TextUtils.isEmpty(mPayRequest.prePayToken)) {
                    goSignAndPay(OrderType.SIGNANDPAY, ticketNo, ticketValidateType)
                } else {
                    goPay(OrderType.SIGNANDPAY, ticketNo, ticketValidateType)
                }
            }
        } else {
            if (mPayRequest.isRecharge) {
                goPay(OrderType.RECHARGE, ticketNo, ticketValidateType)
            } else {
                goPay(OrderType.PAYMENT, ticketNo, ticketValidateType)
            }
        }
    }

    /**
     * 获取下单支付渠道
     */
    private fun getPayType(orderType: OrderType): String? {
        return if (attachCheckBox) { //选择购买位
            //没有被选中或者是不可用的状态 【场景：选择加购位后，所有渠道均不支持】
            if (currentChannel?.disable == true || currentChannel?.isChecked == false) {
                null
            } else {
                currentChannel?.paymentCode
            }
        } else {
            isEnough?.let {
                if (it && orderType.type != OrderType.SIGN.type && orderType.type != OrderType.SIGNANDPAY.type) {
                    //纯可币支付，预下单签约调用支付接口，虚拟资产全部抵扣后，支付方式要传真实签约渠道id
                    PAY_TYPE_VIRTUAL
                } else {
                    currentChannel?.paymentCode
                }
            } ?: currentChannel?.paymentCode
        }
    }

    private fun validConditions(): Pair<Boolean, String> { //支付条件校验
        currentChannel?.let { channel ->
            //bankplatform#0099
            var paymentCode: String? = channel.paymentCode
            if (channel.paymentCode?.contains("#") == true) {
                paymentCode = channel.paymentCode?.split("#")?.let {
                    it[0]
                }
            }
            if (paymentCode == CHANNEL_BANK || paymentCode == CHANNEL_BANK_LIANLIANPAY) { //银行卡渠道有限额
                //实际支付金额
                val actualAmountBd =
                    BigDecimal(actualAmount).times(
                        BigDecimal(100)
                    )
                        .setScale(0, BigDecimal.ROUND_DOWN)
                //单笔支付限额去掉了
//                channel.bankCardInfo?.let {
//                    val limitAmountBd = BigDecimal(it.toString()).times(BigDecimal(100))
//                        .setScale(0, BigDecimal.ROUND_DOWN)
//                    if (actualAmountBd.compareTo(limitAmountBd) == 1) {
//                        return false to AppRuntime.getAppContext()
//                            .getString(R.string.paytypes_had_exceed_max_limit)
//                    }
//                }
                channel.bankCardInfo?.limitToday?.let {
                    val limitToday = BigDecimal(it.toString()).times(BigDecimal(100))
                        .setScale(0, BigDecimal.ROUND_DOWN)
                    if (actualAmountBd.compareTo(limitToday) == 1) {
                        return false to AppRuntime.getAppContext()
                            .getString(R.string.paytypes_had_exceed_max_limit)
                    }
                }
                channel.bankCardInfo?.limitMonth?.let {
                    val limitMonth = BigDecimal(it.toString()).times(BigDecimal(100))
                        .setScale(0, BigDecimal.ROUND_DOWN)
                    if (actualAmountBd.compareTo(limitMonth) == 1) {
                        return false to AppRuntime.getAppContext()
                            .getString(R.string.paytypes_had_exceed_max_limit)
                    }
                }
            }
        }
        return true to ""
    }

    /**
     * 获取下单购买位信息
     */
    private fun getCombineOrder(): CombineOrder? {
        return if (attachCheckBox) { //选择购买位
            //获取加购模板
            val template =
                combineOrderInfo?.renewTemplates?.find { it.payType == currentChannel?.paymentCode }
            var transType = combineOrderInfo?.transType
            if (isVoucherBuyPlace) {
                CombineOrder(
                    combineOrderInfo?.buyPlaceId ?: "",
                    combineOrderInfo?.amount ?: 0,
                    template?.renewProductCode ?: "",
                    combineOrderInfo?.signNotifyUrl ?: "",
                    transType,
                    template?.desc ?: "",
                    template?.subject ?: ""
                )
            } else {
                CombineOrder(
                    "",
                    combineOrderInfo?.amount ?: 0,
                    template?.renewProductCode ?: "",
                    "",
                    transType,
                    template?.desc ?: "",
                    template?.subject ?: ""
                )
            }
        } else {
            null
        }
    }

    /**
     * 下单是否需要额外补充数据 用来关闭dialog
     */
    private fun isNeedAdditionalInfo(payType: String, paymentType: String = ""): Boolean {
        return ChannelHelper.getChannel(payType, paymentType)?.let {
            it.needAdditionalInfo(payType, paymentType)
        } ?: false
    }

    /**
     * 去支付
     */
    private fun goPay(
        orderType: OrderType,
        ticketNo: String?,
        ticketValidateType: String?
    ): String {
        val payType = getPayType(orderType)
        val validConditions = validConditions()
        if (!validConditions.first) {
            ToastUtil.show(validConditions.second)
            LoadingDialogHelper.dismissPayLoadingDialog(isPayLoading)
            TradeCenterStaticUseCase.tradeCenterChannelLimitFail(
                payReq = mPayRequest,
                bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
                bizCode = TransactionConfirmationStatusCodes.CODE_02_002_0002.statusCode,
                bizResult = BizResult.ERROR.value,
                code = "",
                msg = validConditions.second
            )
            return payType ?: ""
        }
        if (TextUtils.isEmpty(payType)) {
            ToastUtil.show(com.oplus.pay.ui.R.string.channel_not_support_choose_other)
            LoadingDialogHelper.dismissPayLoadingDialog(isPayLoading)
            TradeCenterStaticUseCase.tradeCenterPaymentCodeEmpty(
                payReq = mPayRequest,
                bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
                bizCode = TransactionConfirmationStatusCodes.CODE_02_002_0001.statusCode,
                bizResult = BizResult.ERROR.value,
                code = "",
                msg = AppRuntime.getAppContext()
                    .getString(com.oplus.pay.ui.R.string.channel_not_support_choose_other)
                        + "$attachCheckBox-${currentChannel?.paymentCode}"
                        + "-${currentChannel?.disable}-${currentChannel?.isChecked}"
            )
            return payType ?: ""
        }
        //用虚拟券 但是没有购买位拦截报错
        if (PayActionUseCase.getVirtualAssets(
                assets = assets,
                payReq = mPayRequest,
                actualCreditAmount = actualCreditAmount,
                creditSwitchBoxStatus = creditSwitchBoxStatus,
                currentVoucher = currentVoucher,
                voucherUseAmount = voucherUseAmount,
                combineOrderInfo = combineOrderInfo,
                attachCheckBox = attachCheckBox,
                currentCoin = currentCoin,
                currentCredit = currentCredit
            )?.virtualVoucher == RESULT.YES.type && getCombineOrder() == null
        ) {
            ToastUtil.show(com.oplus.pay.ui.R.string.unknow_error)
            LoadingDialogHelper.dismissPayLoadingDialog(isPayLoading)

            TradeCenterStaticUseCase.tradeCenterUseVirtualAssetsVouFail(
                payReq = mPayRequest,
                bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
                bizCode = TransactionConfirmationStatusCodes.CODE_02_002_0003.statusCode,
                bizResult = BizResult.ERROR.value,
                code = "",
                msg = TransactionConfirmationStatusCodes.CODE_02_002_0003.desc
            )

            return payType ?: ""
        }
        val oriAmount = BigDecimal(mPayRequest.mAmount.toString()).times(BigDecimal(100))
            .setScale(0, BigDecimal.ROUND_DOWN).toPlainString()

        val bindId = if (payType == PAY_TYPE_VIRTUAL) { //虚拟渠道不需要传bindid
            ""
        } else {
            currentChannel?.freePwdInfo?.bindId
        }

        val quickPayConfigCacheViewModel =
            if (activity is ComponentActivity) ViewModelProvider(activity)[QuickPayConfigCacheViewModel::class.java] else null
        val fingerConfig = quickPayConfigCacheViewModel?.queryFingerConfig(payType ?: "")
        val passConfig = quickPayConfigCacheViewModel?.queryPassStartSettingConfig(payType ?: "")

        val orderInfo = PayActionUseCase.buildOrderInfo(
            payRequest = mPayRequest,
            payType = payType ?: "",
            oriAmount = oriAmount,
            asset = assets,
            currentCoin = currentCoin,
            currentCredit = currentCredit,
            actualCreditAmount = actualCreditAmount,
            creditSwitchBoxStatus = creditSwitchBoxStatus,
            currentVoucher = currentVoucher,
            voucherUseAmount = voucherUseAmount,
            combineOrderInfo = combineOrderInfo,
            attachCheckBox = attachCheckBox,
            isVoucherBuyPlace = isVoucherBuyPlace,
            rechargeCard = null,
            currentChannel = currentChannel,
            ticketNo = ticketNo,
            screenType = screenType,
            actualAmount = actualAmount,
            orderType = orderType,
            defaultPayType = defaultPayType,
            bindId = bindId,
            quickPayCacheInfo = QuickPayCacheInfo(
                quickPayStatus = quickPayConfigCacheViewModel?.quickPayStatus?.value?.toJson(),
                fStartAuthResult = fingerConfig?.toJson(),
                pStartSetResult = passConfig?.toJson(),
                tradeTraceUserId = mPayRequest.tradeTraceUserId
            ),
            userName = mPayRequest.userName
        )

        goPayImpl(
            orderType = orderType,
            orderInfo = orderInfo,
            ticketValidateType = ticketValidateType,
            payType = payType ?: "",
            isLogin = if (!TextUtils.isEmpty(mPayRequest.mToken)) "1" else "0"
        )
        return payType ?: ""
    }

    private fun goPayImpl(
        orderType: OrderType,
        orderInfo: OrderInfo,
        ticketValidateType: String?,
        payType: String,
        isLogin: String
    ) {
        activity.run {
            goPay(
                orderType,
                this,
                orderInfo.apply { this.ticketValidateType = ticketValidateType })
                .observe(viewLifecycleOwner) {
                    it?.let {
                        when (it.status) {
                            Status.SUCCESS -> {
                                PayLogUtil.d("PayHelper", "goPay  SUCCESS = ${it}")
                                LoadingDialogHelper.dismissPayLoadingDialog(isPayLoading)
                                it.data?.let { result ->
                                    if (result.isNotEmpty()) {
                                        val payReId= JSONObject(result).optString(Constants.PAY_REQUEST_ID)
                                        val payReqId = JSONObject(result).optString("payReqId")
                                        val contractID = JSONObject(result).optString("contractCode")
                                        payRequestId.value = payReqId ?: payReId
                                        contractCode.value =contractID?:""
                                    }
                                }

                                AutoTrace.get().upload(
                                    TradeCenterSceneTrace.createPayOrderResult(
                                        result = "${it.data}",
                                        code = "${it.code}",
                                        msg = "${it.message}",
                                        partnerId = mPayRequest.mPartnerId,
                                        countryCode = mPayRequest.mCountryCode,
                                        source = mPayRequest.mSource,
                                        order = mPayRequest.mPartnerOrder,
                                        token = mPayRequest.processToken ?: "",
                                        payType = payType,
                                        isLogin = isLogin,
                                        currencyCode = mPayRequest.mCurrencyCode,
                                        amount = "${mPayRequest.mAmount}",
                                        packageName = mPayRequest.mPackageName,
                                        productName = mPayRequest.mProductName,
                                        appVersion = mPayRequest.mAppVersion,
                                        screenType = screenType
                                    )
                                )

                                TradeCenterStaticUseCase.createPayOrderResult(
                                    order = orderInfo.bizExt.partnerOrder,
                                    prePayToken = orderInfo.bizExt.prePayToken ?: "",
                                    payType = orderInfo.payType,
                                    type = orderInfo.type,
                                    autoRenew = orderInfo.bizExt.contractType != OrderType.PAYMENT && orderInfo.bizExt.contractType != OrderType.RECHARGE,
                                    token = orderInfo.bizExt.processToken,
                                    bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
                                    bizCode = TransactionConfirmationStatusCodes.CODE_02_004_0010.statusCode,
                                    bizResult = BizResult.SUCCESS.value,
                                    bizErrorMsg = ""
                                )
                            }

                            Status.ERROR -> {
                                PayLogUtil.d("PayHelper", "goPay  ERROR = ${it}")
                                LoadingDialogHelper.dismissPayLoadingDialog(isPayLoading)

                                if (ViewHelper.checkSafeOrderTip(
                                        activity as ComponentActivity,
                                        it.code,
                                        it.message,
                                        orderInfo,
                                        if (mPayRequest.mToken.isNullOrEmpty()) "0" else "1"
                                    )
                                ) {
                                    return@observe
                                }
                                // 虚拟资产完全抵扣风控校验
                                if (accountVerify(it.code, showCheckSafeTipFunc = {
                                        ViewHelper.checkSafeOrderTip(
                                            activity as ComponentActivity,
                                            it.code,
                                            it.message,
                                            orderInfo,
                                            if (mPayRequest.mToken.isNullOrEmpty()) "0" else "1"
                                        )
                                    })) return@observe



                                if (PayCenterRouter.isChildAccount(it.code)) {//儿童账号
                                    AutoTrace.get().upload(
                                        TradeCenterSceneTrace.childAccountIntercept(
                                            result = "${it.data}",
                                            code = "${it.code}",
                                            msg = "${it.message}",
                                            partnerId = mPayRequest.mPartnerId,
                                            order = mPayRequest.mPartnerOrder,
                                            token = mPayRequest.processToken ?: "",
                                            source = mPayRequest.mSource,
                                            screenType = screenType,
                                            payType = payType,
                                            scene = "pay",
                                            currencyCode = mPayRequest.mCurrencyCode,
                                            amount = "${mPayRequest.mAmount}",
                                            packageName = mPayRequest.mPackageName,
                                            productName = mPayRequest.mProductName,
                                            appVersion = mPayRequest.mAppVersion
                                        )
                                    )
                                    PayLogUtil.w("PayHelper", mutableMapOf<String, String>().apply {
                                        this.putAll(
                                            TradeCenterStaticUseCase.buildCommonLogMapInfo(
                                                payRequest = mPayRequest,
                                                bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
                                                bizResult = BizResult.ERROR.value,
                                                bizCode = TransactionConfirmationStatusCodes.CODE_02_004_0011.statusCode
                                            )
                                        )
                                        put(
                                            BizKeyType.KEY_BIZ_ERROR_MSG.value,
                                            "tradePay= ${it.code}- ${it.message}"
                                        )
                                    })

                                    val callback = ChildAccountVerifyCallback(
                                        mPayRequest,
                                        orderType = orderType,
                                        payType = payType,
                                        staticFun = { payRequest, code, msg, ticket ->

                                            AutoTrace.get().upload(
                                                TradeCenterSceneTrace.childAccountValidate(
                                                    result = "$ticket",
                                                    code = "$code",
                                                    msg = "$msg",
                                                    partnerId = payRequest.mPartnerId,
                                                    order = payRequest.mPartnerOrder,
                                                    token = payRequest.processToken ?: "",
                                                    source = payRequest.mSource,
                                                    screenType = screenType,
                                                    payType = payType,
                                                    scene = "pay",
                                                    currencyCode = payRequest.mCurrencyCode,
                                                    amount = "${payRequest.mAmount}",
                                                    packageName = payRequest.mPackageName,
                                                    productName = payRequest.mProductName,
                                                    appVersion = payRequest.mAppVersion
                                                )
                                            )


                                            TradeCenterStaticUseCase.childAccountValidateResult(
                                                order = orderInfo.bizExt.partnerOrder,
                                                prePayToken = orderInfo.bizExt.prePayToken ?: "",
                                                payType = orderInfo.payType,
                                                type = orderInfo.type,
                                                autoRenew = orderInfo.bizExt.contractType != OrderType.PAYMENT || orderInfo.bizExt.contractType != OrderType.RECHARGE,
                                                token = orderInfo.bizExt.processToken,
                                                bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
                                                bizCode = TransactionConfirmationStatusCodes.CODE_02_004_0008.statusCode,
                                                bizResult = if (ticket.isNullOrEmpty()) BizResult.ERROR.value else BizResult.SUCCESS.value,
                                                bizErrorMsg = if (ticket.isNullOrEmpty()) "$code - $msg" else "",
                                            )

                                        },
                                        nextActionFun = { _, _, _, ticket ->
                                            if (!TextUtils.isEmpty(ticket)) {
                                                dispatchPay(ticket)
                                            }

                                        })

                                    AssetsHelper.goToChildVerify(
                                        "pay",
                                        activity = activity,
                                        mPayRequest.mPackageName,
                                        callback
                                    )
                                } else {
                                    TipsHelper.showErrorTips(
                                        it.code,
                                        it.message,
                                        PayCenterRouter.CODE_LIST
                                    )
                                }

                                AutoTrace.get().upload(
                                    TradeCenterSceneTrace.createPayOrderError(
                                        errorCode = "${it.code}",
                                        errorMsg = "${it.message}",
                                        partnerId = mPayRequest.mPartnerId,
                                        countryCode = mPayRequest.mCountryCode,
                                        source = mPayRequest.mSource,
                                        order = mPayRequest.mPartnerOrder,
                                        token = mPayRequest.processToken ?: "",
                                        payType = payType,
                                        isLogin = isLogin,
                                        currencyCode = mPayRequest.mCurrencyCode,
                                        amount = "${mPayRequest.mAmount}",
                                        packageName = mPayRequest.mPackageName,
                                        productName = mPayRequest.mProductName,
                                        screenType = screenType,
                                        appVersion = mPayRequest.mAppVersion
                                    )
                                )

                                TradeCenterStaticUseCase.createPayOrderResult(
                                    order = orderInfo.bizExt.partnerOrder,
                                    prePayToken = orderInfo.bizExt.prePayToken ?: "",
                                    payType = orderInfo.payType,
                                    type = orderInfo.type,
                                    autoRenew = orderInfo.bizExt.contractType != OrderType.PAYMENT || orderInfo.bizExt.contractType != OrderType.RECHARGE,
                                    token = orderInfo.bizExt.processToken,
                                    bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
                                    bizCode = TransactionConfirmationStatusCodes.CODE_02_004_0010.statusCode,
                                    bizResult = BizResult.ERROR.value,
                                    bizErrorMsg = "${it.code}-${it.message}",
                                )

                            }

                            Status.LOADING -> {
                                val checkSafeCancel =
                                    (it.code == FingerPrintCode.ERROR_CAPTCHA_CANCEL.type.toString())
                                PayLogUtil.e("checkSafeCancel:$checkSafeCancel")
                                if (isNeedAdditionalInfo(
                                        orderInfo.payType,
                                        orderInfo.paymentType
                                    ) || checkSafeCancel
                                ) { //流程结束了关闭弹框
                                    LoadingDialogHelper.dismissPayLoadingDialog(isPayLoading)
                                }
                            }
                        }
                    }
                }
        }
    }


    /**
     * 去支付
     */
    private fun goPay(
        orderType: OrderType,
        activity: Activity,
        orderInfo: OrderInfo
    ): LiveData<Resource<String>> {
        return PayActionUseCase.goPay(
            orderType = orderType,
            activity = activity,
            orderInfo = orderInfo
        )
    }

    private fun accountVerify(
        code: String?,
        showCheckSafeTipFunc: () -> Unit
    ): Boolean {
        if (AccountVerifyHelper.needAccountVerify(code)) {
            AccountVerifyHelper.verify(
                activity = activity,
                processToken = mPayRequest.processToken ?: "",
                token = mPayRequest.mToken,
                nextActionFunc = { code, _, ticket ->
                    //校验SDK验证失败弹帐号风险弹窗
                    if (AccountVerifyHelper.checkVerifyFail(code)) {
                        showCheckSafeTipFunc.invoke()
                        PayLogUtil.e("PayHelper", mutableMapOf<String, String>().apply {
                            this.putAll(
                                TradeCenterStaticUseCase.buildCommonLogMapInfo(
                                    payRequest = mPayRequest,
                                    bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
                                    bizResult = BizResult.ERROR.value,
                                    bizCode = TransactionConfirmationStatusCodes.CODE_02_004_0009.statusCode
                                )
                            )
                            put(BizKeyType.KEY_BIZ_ERROR_MSG.value, "checkVerifyFail")
                        })
                        return@verify
                    }
                    // 校验成功继续下单
                    if (!TextUtils.isEmpty(ticket)) {
                        dispatchPay(ticket, TicketValidateType.RISK_VALID_TICKET.value)
                    } else {
                        PayLogUtil.e("PayHelper", mutableMapOf<String, String>().apply {
                            this.putAll(
                                TradeCenterStaticUseCase.buildCommonLogMapInfo(
                                    payRequest = mPayRequest,
                                    bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
                                    bizResult = BizResult.ERROR.value,
                                    bizCode = TransactionConfirmationStatusCodes.CODE_02_004_0009.statusCode
                                )
                            )
                            put(BizKeyType.KEY_BIZ_ERROR_MSG.value, "ticket is empty")
                        })
                    }
                })
            return true
        }
        return false
    }

    /**
     * 获取签约类型
     */
    private fun getTransType(): String {
        return if (mPayRequest.mAutoRenew == OrderType.SIGN.ori) {//-仅签约
            TransType.SIGN.type
        } else {//签约并支付
            TransType.SIGNANDPAY.type
        }
    }


    private fun getSignPayType(): String {
        return if (currentChannel?.disable == true || currentChannel?.isChecked == false) {
            ""
        } else {
            currentChannel?.paymentCode ?: ""
        }
    }

    private fun goSignAndPay(
        orderType: OrderType,
        ticketNo: String?,
        ticketValidateType: String?
    ): String {
        val signPayType = getSignPayType()
        if (TextUtils.isEmpty(signPayType)) {
            ToastUtil.show(com.oplus.pay.ui.R.string.channel_not_support_choose_other)
            LoadingDialogHelper.dismissPayLoadingDialog(isPayLoading)
            return signPayType
        }
        val oriAmount = BigDecimal(mPayRequest.mAmount.toString()).times(BigDecimal(100))
            .setScale(0, BigDecimal.ROUND_DOWN).toPlainString()

        val signInfo = PayActionUseCase.buildSignInfo(
            payRequest = mPayRequest,
            payType = getSignPayType(),
            oriAmount = oriAmount,
            asset = assets,
            currentCoin = currentCoin,
            currentCredit = currentCredit,
            actualCreditAmount = actualCreditAmount,
            creditSwitchBoxStatus = creditSwitchBoxStatus,
            currentVoucher = currentVoucher,
            voucherUseAmount = voucherUseAmount,
            combineOrderInfo = combineOrderInfo,
            attachCheckBox = attachCheckBox,
            ticketNo = ticketNo,
            screenType = screenType,
            actualAmount = actualAmount,
            orderType = orderType,
            defaultPayType = defaultPayType

        )
        // 签约支付创建订单
        signAndPayImpl(
            signInfo = signInfo,
            ticketValidateType = ticketValidateType,
            isLogin = if (!TextUtils.isEmpty(mPayRequest.mToken)) "1" else "0",
            orderType = orderType
        )

        return signPayType
    }

    private fun signAndPayImpl(
        signInfo: SignInfo,
        ticketValidateType: String?,
        isLogin: String,
        orderType: OrderType
    ) {
        PayActionUseCase.goSignAndPay(
            activity,
            signInfo.apply { this.ticketValidateType = ticketValidateType })
            .observe(activity as LifecycleOwner) {
                when (it.status) {
                    Status.SUCCESS -> {
                        LoadingDialogHelper.dismissPayLoadingDialog(isPayLoading)
                        it.data?.let { result ->
                            if (result.isNotEmpty()) {
                                val payReId= JSONObject(result).optString(Constants.PAY_REQUEST_ID)
                                val payReqId = JSONObject(result).optString("payReqId")
                                val contractID = JSONObject(result).optString("contractCode")
                                payRequestId.value = payReqId ?: payReId
                                contractCode.value =contractID?:""
                            }
                        }
                        AutoTrace.get().upload(
                            TradeCenterSceneTrace.createPayOrderResult(
                                result = "${it.data}",
                                code = "${it.code}",
                                msg = "${it.message}",
                                partnerId = mPayRequest.mPartnerId,
                                countryCode = mPayRequest.mCountryCode,
                                source = mPayRequest.mSource,
                                order = mPayRequest.mPartnerOrder,
                                token = mPayRequest.processToken ?: "",
                                payType = getSignPayType() ?: "",
                                isLogin = isLogin,
                                currencyCode = mPayRequest.mCurrencyCode,
                                amount = "${mPayRequest.mAmount}",
                                packageName = mPayRequest.mPackageName,
                                productName = mPayRequest.mProductName,
                                appVersion = mPayRequest.mAppVersion,
                                screenType = screenType
                            )
                        )
                    }

                    Status.ERROR -> {
                        LoadingDialogHelper.dismissPayLoadingDialog(isPayLoading)
                        if (ViewHelper.checkSafeSignTip(
                                activity as ComponentActivity,
                                it.code,
                                it.message,
                                signInfo,
                                if (mPayRequest.mToken.isNullOrEmpty()) "0" else "1"
                            )
                        ) {
                            return@observe
                        }

                        // 虚拟资产完全抵扣风控校验
                        if (accountVerify(it.code, showCheckSafeTipFunc = {
                                ViewHelper.checkSafeSignTip(
                                    activity as ComponentActivity,
                                    it.code,
                                    it.message,
                                    signInfo,
                                    if (mPayRequest.mToken.isNullOrEmpty()) "0" else "1"
                                )
                            })) return@observe


                        if (PayCenterRouter.isChildAccount(it.code)) {//儿童账号
                            val scene = if (getTransType() == TransType.SIGN.type) {
                                "sign"
                            } else {
                                "signandpay"
                            }
                            AutoTrace.get().upload(
                                TradeCenterSceneTrace.childAccountIntercept(
                                    result = "${it.data}",
                                    code = "${it.code}",
                                    msg = "${it.message}",
                                    partnerId = mPayRequest.mPartnerId,
                                    order = mPayRequest.mPartnerOrder,
                                    token = mPayRequest.processToken ?: "",
                                    source = mPayRequest.mSource,
                                    screenType = screenType,
                                    payType = getSignPayType(),
                                    scene = "pay",
                                    currencyCode = mPayRequest.mCurrencyCode,
                                    amount = "${mPayRequest.mAmount}",
                                    packageName = mPayRequest.mPackageName,
                                    productName = mPayRequest.mProductName,
                                    appVersion = mPayRequest.mAppVersion
                                )
                            )

                            val callback = ChildAccountVerifyCallback(
                                mPayRequest,
                                orderType = orderType,
                                payType = getSignPayType(),
                                staticFun = { payRequest, code, msg, ticket ->
                                    AutoTrace.get().upload(
                                        TradeCenterSceneTrace.childAccountValidate(
                                            result = "$ticket",
                                            code = "$code",
                                            msg = "$msg",
                                            partnerId = payRequest.mPartnerId,
                                            order = payRequest.mPartnerOrder,
                                            token = payRequest.processToken ?: "",
                                            source = payRequest.mSource,
                                            screenType = screenType,
                                            payType = getSignPayType(),
                                            scene = "pay",
                                            currencyCode = payRequest.mCurrencyCode,
                                            amount = "${payRequest.mAmount}",
                                            packageName = payRequest.mPackageName,
                                            productName = payRequest.mProductName,
                                            appVersion = payRequest.mAppVersion
                                        )
                                    )

                                },
                                nextActionFun = { _, _, _, ticket ->
                                    if (!TextUtils.isEmpty(ticket)) {
                                        dispatchPay(ticket)
                                    }
                                })

                            AssetsHelper.goToChildVerify(
                                scene,
                                activity = activity,
                                mPayRequest.mPackageName,
                                callback
                            )
                        } else {
                            TipsHelper.showErrorTips(it.code, it.message, PayCenterRouter.CODE_LIST)
                        }
                        AutoTrace.get().upload(
                            TradeCenterSceneTrace.createSignPayOrderError(
                                errorCode = "${it.code}",
                                errorMsg = "${it.message}",
                                partnerId = mPayRequest.mPartnerId,
                                countryCode = mPayRequest.mCountryCode,
                                source = mPayRequest.mSource,
                                order = mPayRequest.mPartnerOrder,
                                token = mPayRequest.processToken ?: "",
                                payType = getSignPayType(),
                                isLogin = isLogin,
                                currencyCode = mPayRequest.mCurrencyCode,
                                amount = "${mPayRequest.mAmount}",
                                packageName = mPayRequest.mPackageName,
                                productName = mPayRequest.mProductName,
                                screenType = screenType,
                                appVersion = mPayRequest.mAppVersion
                            )
                        )
                    }

                    Status.LOADING -> {
                        val checkSafeCancel =
                            (it.code == FingerPrintCode.ERROR_CAPTCHA_CANCEL.type.toString())
                        PayLogUtil.e("checkSafeCancel:$checkSafeCancel")
                        if (checkSafeCancel) { //校验取消 关闭弹窗
                            LoadingDialogHelper.dismissPayLoadingDialog(isPayLoading)
                        }
                    }
                }
            }
    }

    /**
     * 签约并支付
     */
    fun signAndPay(activity: Activity, signInfo: SignInfo) =
        PayCenterHelper.signAndPay(activity, signInfo)
}