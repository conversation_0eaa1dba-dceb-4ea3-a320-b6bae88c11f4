{"_content": " 交易中心业务埋点  https://doc.myoas.com/pages/viewpage.action?pageId=495879876", "events": [{"method_id": "pay_center_exposure", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_exposure", "isLogin": "%s", "appTime": "%s", "sdkTime": "%s", "screen_type": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "currencyCode": "%s", "amount": "%s", "systime": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "contract_type": "%s", "is_direct ": "%s", "dcs_upload": "enable"}, {"method_id": "pay_type_exposure", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_types_exposure", "pay_type": "%s", "payments_exposure_page": "pay", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "isLogin": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "systime": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "contract_type": "%s", "is_direct": "%s", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "event_id_paytypes_exposure_fail", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_paytypes_exposure_fail", "fail_code": "%s", "token": "%s", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "event_id_speed_exposure", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_speed_exposure", "token": "%s", "order": "%s", "partnerId": "%s", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "source": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "isLogin": "%s", "screen_type": "%s", "prePayToken": "%s", "isPrePay": "%s", "DefaultPay": "%s", "type": "view"}, {"method_id": "pay_types_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_types_click", "pay_type": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "isLogin": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "is_show": "%s", "is_auto": "%s", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "pay_center_out_dialog_exposure", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_out_dialog_exposure", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "is_service_information": "%s", "dialog_content": "%s", "dialog_voucherId": "%s", "from": "%s", "screen_type": "%s", "trackId": "%s", "type": "view"}, {"method_id": "pay_center_out_dialog_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_out_dialog_click_os", "dialog_click_type": "%s", "token": "%s", "from": "%s", "trackId": "%s", "type": "click", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "user_has_balance", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_user_has_balance", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "uploadNow": "Y"}, {"method_id": "koko_coin_charge", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_koko_coin_charge", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "koko_coin_charge_btn_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_koko_coin_charge_btn_click", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "payments_load_retry", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_payments_load_retry", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "pay_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_click", "isLogin": "%s", "is_ticket_finished": "%s", "pay_type": "%s", "screen_type": "%s", "pay_button_type": "%s", "credit_num": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "contract_type": "%s", "is_direct": "%s", "add_id": "%s", "is_quick": "%s", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "event_id_app_start_cost_time", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_app_start_cost_time", "screen_type": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "systime": "%s", "partnerId": "%s", "currencyCode": "%s", "packageName": "%s", "app_version": "%s", "contract_type": "%s", "is_direct": "%s", "versionCode": "%s", "start_cost": "%s", "isPrePay": "%s"}, {"method_id": "payments_finz", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_payments_finz", "payments_exposure_page": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "payments_load_error", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_payments_load_error", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "setting_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_setting_click", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "pay_center_more_channel_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_more_channel_click", "partnerId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "show_paytype": "%s"}, {"method_id": "speaker_show", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_speaker_show", "page": "speaker_buy", "speakerID": "%s", "content": "%s", "partnerId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s"}, {"method_id": "speaker_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_speaker_click", "page": "speaker_buy", "speakerID": "%s", "content": "%s", "partnerId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s"}, {"method_id": "create_pay_order", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_create_pay_order", "pay_order_info": "%s", "type": "pay", "partnerId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "isLogin": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "screen_type": "%s", "app_version": "%s", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "create_sign_pay_order", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_create_sign_pay_order", "pay_order_info": "%s", "type": "sign_pay", "partnerId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "isLogin": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "screen_type": "%s", "app_version": "%s", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "create_pay_order_result", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_create_pay_order_result", "type": "pay", "result": "%s", "code": "%s", "msg": "%s", "partnerId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "pay_type": "%s", "isLogin": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "screen_type": "%s", "app_version": "%s", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "create_pay_order_error", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_create_pay_order_error", "type": "pay", "errorCode": "%s", "errorMsg": "%s", "partnerId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "pay_type": "%s", "isLogin": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "screen_type": "%s", "app_version": "%s", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "create_sign_pay_order_error", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_create_sign_pay_order_error", "type": "sign_pay", "errorCode": "%s", "errorMsg": "%s", "partnerId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "pay_type": "%s", "isLogin": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "screen_type": "%s", "app_version": "%s", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "show_buy_place", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_show_buy_place", "buyPlaceId": "%s", "packageName": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "uploadNow": "Y"}, {"method_id": "buy_place_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_buy_place_click", "selected": "%s", "transType": "%s", "packageName": "%s", "buyPlaceId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "buy_place_success", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_buy_place_success", "payOrder": "%s", "buyPlaceOrder": "%s", "selected": "%s", "packageName": "%s", "buyPlaceId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "show_more_vou", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_show_more_vou", "moreVouInfo": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "more_vou_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_more_vou_click", "moreVouInfo": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "pay_center_vou_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_vou_click", "availableNum": "%s", "detailedVou": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "is_add": "%s", "is_kbcoupon": "%s", "is_await": "%s", "coupon_name": "%s", "type": "click"}, {"method_id": "paycenter_time", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_in_paycenter_time", "time": "%s", "screen_type": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "click_buy_place_private_info", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_click_buy_place_private_info", "select": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "pri_type": "%s", "partnerId": "%s", "type": "%s"}, {"method_id": "click_credit_status", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_click_credit_status", "select": "%s", "screen_type": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s"}, {"method_id": "item_vou_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_kbcoupon_click", "moreVouInfo": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "vip_kbcoupon_id": "%s", "kbcoupon_id": "%s", "result_id": "%s", "coupon_type": "%s", "btn_statues": "%s", "type": "click"}, {"method_id": "item_vou_show", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_kbcoupon_show", "moreVouInfo": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "vip_kbcoupon_id": "%s", "kbcoupon_id": "%s", "type": "view"}, {"method_id": "event_id_kbcoupon_not_used", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_kbcoupon_not_used", "type": "click", "token": "%s"}, {"method_id": "event_id_kbcoupon_cannot_toast", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_kbcoupon_cannot_toast", "type": "view", "token": "%s", "kbcoupon_id": "%s", "coupon_type": "%s", "toast_text": "%s"}, {"method_id": "kbcoupon_entry_show", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_kbcoupon_entry_show", "partnerId": "%s", "order": "%s", "token": "%s", "source": "%s", "is_add": "%s", "is_kbcoupon": "%s", "is_await": "%s", "screen_type": "%s", "coupon_name": "%s", "type": "view"}, {"method_id": "kecoin_enter", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_kecoin_enter", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "source": "%s", "packageName": "%s", "productName": "%s", "isLogin": "%s", "screen_type": "%s", "order": "%s", "partnerId": "%s", "ssoid": "", "token": "%s", "type": "click"}, {"method_id": "kecoin_deduct_clk", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_kecoin_deduct_clk", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "source": "%s", "packageName": "%s", "productName": "%s", "isLogin": "%s", "screen_type": "%s", "order": "%s", "partnerId": "%s", "ssoid": "", "token": "%s", "type": "click", "choose_dedect_type": "%s"}, {"method_id": "kecoin_deduct_fail", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_kecoin_deduct_fail", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "source": "%s", "packageName": "%s", "productName": "%s", "isLogin": "%s", "screen_type": "%s", "order": "%s", "partnerId": "%s", "ssoid": "", "token": "%s", "type": "view", "fail_reason": "%s"}, {"method_id": "credit_enter", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_credit_enter", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "source": "%s", "packageName": "%s", "productName": "%s", "isLogin": "%s", "screen_type": "%s", "order": "%s", "partnerId": "%s", "ssoid": "", "token": "%s", "type": "click"}, {"method_id": "platform_limit_discount", "categoryId": "2015101", "log_tag": "2015101", "event_id": "platform_limit_discount", "type": "view", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "PayVersionName": "%s", "PayVersionCode": "%s", "isLogin": "%s", "imei": "%s", "ouid": "%s", "guid": "%s", "os_version": "%s", "partnerId": "%s", "ssoid": "", "order": "%s", "token": "%s", "source": "%s", "screen_type": "%s", "pay_type": "%s", "activity_id": "%s", "discount": "%s"}, {"method_id": "show_credit_info", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_show_credit_info", "partnerId": "%s", "order": "%s", "token": "%s", "source": "%s", "creditEnabled": "%s", "creditCount": "%s", "creditRate": "%s", "creditConditionDesc": "%s", "creditDesc": "%s"}, {"method_id": "show_credit_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_show_credit_click", "partnerId": "%s", "order": "%s", "token": "%s", "source": "%s", "credit_count": "%s", "credit_rate": "%s", "action": "%s"}, {"method_id": "tips_icon_show", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_tips_icon_show", "partnerId": "%s", "order": "%s", "token": "%s", "source": "%s", "screen_type": "%s"}, {"method_id": "tips_icon_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_tips_icon_click", "partnerId": "%s", "order": "%s", "token": "%s", "source": "%s", "screen_type": "%s"}, {"method_id": "child_account_intercept", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_child_account_intercept", "result": "%s", "code": "%s", "msg": "%s", "partnerId": "%s", "order": "%s", "token": "%s", "source": "%s", "screen_type": "%s", "pay_type": "%s", "scene": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "child_account_validate", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_child_account_validate", "result": "%s", "code": "%s", "msg": "%s", "partnerId": "%s", "order": "%s", "token": "%s", "source": "%s", "screen_type": "%s", "pay_type": "%s", "scene": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "dispatch_pay_way", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_dispatch_pay_way", "pay_way": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "isLogin": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "pay_center_out_dialog_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_out_dialog_click", "country_code": "%s", "dialog_click_type": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "pay_coupon_list", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_coupon_list_os", "country_code": "%s", "vou_list": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "coupon_isable_count", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_coupon_isable_count_os", "country_code": "%s", "count": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "coupon_default_selected", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_coupon_default_selected_os", "country_code": "%s", "vou_value": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "koko_coin_value_selected", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_koko_coin_value_selected_os", "country_code": "%s", "koko_coin_value_selected": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "login_dialog_exposure", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_login_dialog_exposure_os", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "login_dialog_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_login_dialog_click_os", "dialog_click_type": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "paycenter_open_login_result", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_paycenter_open_login_result_os", "channel": "%s", "pay_type": "%s", "result": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "default_channel", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_default_channel_os", "channel": "%s", "pay_type": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "payments_card_more_os", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_payments_bank_card_more_os", "country_code": "%s", "channel": "%s", "pay_type": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "pay_center_use_coCoin", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_use_coCoin", "channel": "%s", "pay_type": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "pay_center_coCoin_deduction_dialog", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_coCoin_deduction_dialog", "channel": "%s", "pay_type": "%s", "kebiAmount": "%s", "cocoinPayAmount": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "pay_center_coCoin_deduction_dialog_btn", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_coCoin_deduction_dialog_btn", "kebiAmount": "%s", "cocoinPayAmount": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "screen_type": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s"}, {"method_id": "event_id_dispatch_pay_way", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_dispatch_pay_way", "payWay": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "partnerId": "%s", "currencyCode": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "screen_type": "%s"}, {"log_tag": "2015101", "categoryId": "2015101", "event_id": "event_id_more_paytype_fold_show", "token": "%s", "order": "%s", "partnerId": "%s", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "source": "%s", "packageName": "%s", "productName": "%s", "isLogin": "%s", "screen_type": "%s", "prePayToken": "%s", "isPrePay": "%s", "DefaultPay": "%s", "default_paytype": "%s"}, {"method_id": "event_id_fanxin_guide_toast", "log_tag": "2015101", "categoryId": "2015101", "event_id": "event_id_fanxin_guide_toast", "token": "%s", "order": "%s", "partnerId": "%s", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "source": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "isLogin": "%s", "screen_type": "%s", "prePayToken": "%s", "isPrePay": "%s", "DefaultPay": "%s", "trackId": "%s", "message_id": "%s", "is_add": "%s"}, {"log_tag": "2015101", "categoryId": "2015101", "event_id": "event_id_speed_discontinue", "token": "%s", "order": "%s", "partnerId": "%s", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "source": "%s", "packageName": "%s", "productName": "%s", "isLogin": "%s", "screen_type": "%s", "prePayToken": "%s", "isPrePay": "%s", "DefaultPay": "%s", "default_paytype": "%s", "cancel_time": "%s"}, {"method_id": "event_id_pay_types_label", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_types_label", "token": "%s", "type": "%s", "label_detail": "%s", "pay_type": "%s", "trackId": "%s"}, {"method_id": "event_id_quick_pay_label", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_quick_pay_label", "token": "%s", "type": "%s", "label_detail": "%s", "pay_type": "%s", "trackId": "%s"}, {"method_id": "create_sign_pay_order_result", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_create_sign_pay_order_result", "type": "sign_pay", "result": "%s", "code": "%s", "msg": "%s", "partnerId": "%s", "country_code": "%s", "source": "%s", "order": "%s", "token": "%s", "pay_type": "%s", "isLogin": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "screen_type": "%s", "app_version": "%s"}, {"method_id": "event_id_pay_center_out_dialog_result", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_out_dialog_result", "token": "%s", "type": "request", "trackId": "%s", "result_id": "%s", "timestamp": "%s"}, {"method_id": "event_id_add_privacy_dialog", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_add_privacy_dialog", "order": "%s", "token": "%s", "add_id": "%s", "type": "%s"}, {"method_id": "event_id_add_privacy_dialog_btn", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_add_privacy_dialog_btn", "order": "%s", "token": "%s", "add_id": "%s", "btn_text": "%s", "type": "%s"}, {"method_id": "event_id_show_fingerprint_verify_dialog", "categoryId": "2015101", "log_tag": "2015101", "event_id": "fingerprint_verify_dialog", "type": "view", "ssoid": "", "reqpkg": "%s", "paytype": "%s", "token": "%s"}, {"method_id": "event_id_fingerprint_verify_result", "categoryId": "2015101", "log_tag": "2015101", "event_id": "fingerprint_verify_result", "type": "request", "ssoid": "", "reqpkg": "%s", "paytype": "%s", "token": "%s", "result_id": "%s", "code": "%s", "message": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "bizErrorMsg": "%s", "payBizScene": "%s", "report": "DCSPR", "dcs_upload": "enable"}, {"method_id": "event_id_quick_pay_fail", "categoryId": "2015101", "log_tag": "2015101", "event_id": "quick_pay_fail", "type": "request", "ssoid": "", "reqpkg": "%s", "paytype": "%s", "token": "%s", "result_id": "%s"}, {"method_id": "event_id_screen_adjust_init_direction", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_screen_adjust_init_direction", "partnerId": "%s", "order": "%s", "prePayToken": "%s", "token": "%s", "isNeedLocked": "%s", "enableScreen": "%s", "currentPage": "%s", "direction": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1"}, {"method_id": "event_id_screen_adjust_rotation", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_screen_adjust_rotation", "partnerId": "%s", "order": "%s", "prePayToken": "%s", "token": "%s", "isNeedLocked": "%s", "enableScreen": "%s", "lastPage": "%s", "currentPage": "%s", "lastDirection": "%s", "currentDirection": "%s"}, {"method_id": "recharge_kbcoupon_tips_dialog", "categoryId": "2015101", "log_tag": "2015101", "event_id": "recharge_kbcoupon_tips_dialog", "type": "view", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "source": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "PayVersionName": "%s", "PayVersionCode": "%s", "isLogin": "%s", "os_version": "%s", "imei": "%s", "ouid": "%s", "guid": "%s", "screen_type": "%s", "order": "%s", "partnerId": "%s"}, {"method_id": "recharge_kbcoupon_tips_dialog_clk", "categoryId": "2015101", "log_tag": "2015101", "event_id": "recharge_kbcoupon_tips_dialog_clk", "type": "click", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "source": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "PayVersionName": "%s", "PayVersionCode": "%s", "isLogin": "%s", "os_version": "%s", "imei": "%s", "ouid": "%s", "guid": "%s", "screen_type": "%s", "order": "%s", "partnerId": "%s", "token": "%s", "reqpkg": "%s", "btn_id": "%s"}, {"method_id": "kebi_detail_pay", "categoryId": "2015101", "log_tag": "2015101", "event_id": "kebi_detail_pay", "type": "click", "country_code": "%s", "currencyCode": "%s", "amount": "%s", "packageName": "%s", "productName": "%s", "app_version": "%s", "PayVersionName": "%s", "PayVersionCode": "%s", "isLogin": "%s", "os_version": "%s", "imei": "%s", "ouid": "%s", "guid": "%s", "screen_type": "%s", "order": "%s", "partnerId": "%s", "ssoid": "%s", "reqpkg": "%s", "token": "%s", "cnt": "%s", "pay_type": "%s"}, {"method_id": "trade_center_activity_oncreate", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_activity_oncreate", "order": "%s", "prePayToken": "%s", "token": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "TPR", "dataType": "1"}, {"method_id": "trade_center_dispatch_paytype", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_dispatch_paytype", "order": "%s", "prePayToken": "%s", "token": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "TPR", "payCategory": "%s", "dataType": "1"}, {"method_id": "trade_center_jump_channel_pay", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_jump_channel_pay", "order": "%s", "prePayToken": "%s", "token": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "TPR", "screenType": "%s", "dataType": "1"}, {"method_id": "trade_center_pay_center_exposure", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_pay_center_exposure", "order": "%s", "prePayToken": "%s", "token": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "tradeCenterType": "%s", "report": "DCSPR", "screenType": "%s", "dataType": "1", "dcs_upload": "enable", "uploadNow": "Y"}, {"method_id": "paytypes_exposure_fail", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_paytypes_exposure_error", "fail_code": "%s", "token": "%s", "dcs_upload": "enable", "order": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "bizErrorMsg": "%s", "report": "DCSPR", "dataType": "1"}, {"method_id": "payment_code_exposure", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_payment_code_exposure", "pay_type": "%s", "payments_exposure_page": "pay", "token": "%s", "dcs_upload": "enable", "order": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "tradeCenterType": "%s"}, {"method_id": "quick_payment_code_exposure", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_quick_payment_code_exposure", "pay_type": "%s", "payments_exposure_page": "pay", "token": "%s", "dcs_upload": "enable", "order": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "tradeCenterType": "%s"}, {"method_id": "quick_payment_code_exposure_fail", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_quick_payment_code_exposure_fail", "payments_exposure_page": "pay", "fail_code": "%s", "bizErrorMsg": "%s", "token": "%s", "dcs_upload": "enable", "order": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1"}, {"method_id": "calculate_server_fail", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_calculate_server_fail", "code": "%s", "msg": "%s", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "bizErrorMsg": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "trade_center_pay_btn_click", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_pay_btn_click", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable", "screenType": "%s", "attachCheckBox": "%s", "currentChannel": "%s", "currentVoucher": "%s", "combineOrderInfo": "%s", "actualAmount": "%s"}, {"method_id": "trade_center_channel_limit_fail", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_channel_limit_fail", "code": "%s", "msg": "%s", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "bizErrorMsg": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "trade_center_payment_code_empty", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_payment_code_empty", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "bizErrorMsg": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "trade_center_use_virtual_assets_vou_fail", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_use_virtual_assets_vou_fail", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "bizErrorMsg": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "trade_center_start_confirm_channel_info", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_start_confirm_channel_info", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "trade_center_start_confirm_channel_info_fail", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_start_confirm_channel_info_fail", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "bizErrorMsg": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "trade_center_start_confirm_channel_info_success", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_start_confirm_channel_info_success", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "start_init_channel_router_info", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_start_init_channel_router_info", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "trade_center_init_channel_router_info_fail", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_init_channel_router_info_fail", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "bizErrorMsg": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "trade_center_init_channel_router_info_success", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_init_channel_router_info_success", "order": "%s", "token": "%s", "prePayToken": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "trade_create_pay_order_result", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_create_pay_order_result", "order": "%s", "token": "%s", "prePayToken": "%s", "pay_type": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "bizErrorMsg": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable"}, {"method_id": "child_account_validate", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_child_account_validate", "order": "%s", "token": "%s", "prePayToken": "%s", "pay_type": "%s", "bizNode": "%s", "bizCode": "%s", "bizResult": "%s", "payBizScene": "%s", "report": "DCSPR", "dataType": "1", "dcs_upload": "enable", "bizErrorMsg": "%s"}, {"method_id": "quick_pay_guide", "categoryId": "2015101", "log_tag": "2015101", "event_id": "quick_pay_guide", "token": "%s", "bizNode": "%s", "order": "%s", "prePayToken": "%s", "type": "view", "pay_type": "%s", "trackId": "%s"}, {"method_id": "kebi_recharge_page", "categoryId": "2015101", "log_tag": "2015101", "event_id": "kebi_recharge_page", "token": "%s", "type": "view"}, {"method_id": "kebi_recharge_clk", "categoryId": "2015101", "log_tag": "2015101", "event_id": "kebi_recharge_clk", "token": "%s", "type": "click", "kebi_balance": "%s", "kebi_recharge": "%s", "is_action": "%s"}, {"method_id": "kebi_recharge_serve", "categoryId": "2015101", "log_tag": "2015101", "event_id": "kebi_recharge_serve", "token": "%s", "type": "click"}, {"method_id": "kebi_recharge_manual", "categoryId": "2015101", "log_tag": "2015101", "event_id": "kebi_recharge_manual", "token": "%s", "type": "click"}, {"method_id": "recharge_agreement_dialog", "categoryId": "2015101", "log_tag": "2015101", "event_id": "recharge_agreement_dialog", "token": "%s", "type": "view", "kebi_balance": "%s", "kebi_recharge": "%s"}, {"method_id": "recharge_agreement_dialog_clk", "categoryId": "2015101", "log_tag": "2015101", "event_id": "recharge_agreement_dialog_clk", "token": "%s", "type": "click", "kebi_balance": "%s", "kebi_recharge": "%s", "btn_id": "%s"}, {"method_id": "kebi_recharge_banner", "categoryId": "2015101", "log_tag": "2015101", "event_id": "kebi_recharge_banner", "token": "%s", "type": "view", "trackId": "%s"}, {"method_id": "kebi_recharge_banner_clk", "categoryId": "2015101", "log_tag": "2015101", "event_id": "kebi_recharge_banner_clk", "token": "%s", "type": "click", "trackId": "%s", "jump_url": "%s"}, {"method_id": "trade_center_query_result", "categoryId": "2015101", "log_tag": "2015101", "event_id": "event_id_trade_center_query_result", "token": "%s", "paymentCode": "%s", "countryCode": "%s", "prePayToken": "%s", "payReqId": "%s", "contractCode": "%s"}]}