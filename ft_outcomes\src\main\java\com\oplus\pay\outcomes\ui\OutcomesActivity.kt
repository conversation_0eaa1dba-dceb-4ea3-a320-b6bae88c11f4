package com.oplus.pay.outcomes.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Html
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.view.KeyEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.activity.viewModels
import com.coui.appcompat.button.SingleButtonWrap
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.oplus.pay.basic.AppRuntime
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.basic.Status
import com.oplus.pay.basic.util.math.CurrencyHelper
import com.oplus.pay.basic.util.serialize.GSON
import com.oplus.pay.basic.util.ui.DisplayHelper
import com.oplus.pay.basic.util.ui.StatusBarHelper
import com.oplus.pay.basic.util.ui.ToastUtil
import com.oplus.pay.biz.BizHelper
import com.oplus.pay.biz.FastPayStatus
import com.oplus.pay.biz.TransType
import com.oplus.pay.diff.DiffUpgradeHelper
import com.oplus.pay.monitor.api.d
import com.oplus.pay.monitor.api.model.BizNode
import com.oplus.pay.monitor.api.model.BizResult
import com.oplus.pay.monitor.api.model.TransactionResultStatusCodes
import com.oplus.pay.outcomes.OutcomesCode
import com.oplus.pay.outcomes.OutcomesHelper
import com.oplus.pay.outcomes.OutcomesParam
import com.oplus.pay.outcomes.R
import com.oplus.pay.outcomes.api.OutcomesRouter
import com.oplus.pay.outcomes.databinding.OpayFtOutcomesActivityOutcomesBinding
import com.oplus.pay.outcomes.model.NOTIFY_STATUS_OK
import com.oplus.pay.outcomes.model.OutcomesPrePayResponse
import com.oplus.pay.outcomes.model.OutcomesResponse
import com.oplus.pay.outcomes.model.OutcomesSignResponse
import com.oplus.pay.outcomes.model.STATUS_FAIL
import com.oplus.pay.outcomes.model.STATUS_INIT
import com.oplus.pay.outcomes.model.STATUS_OK
import com.oplus.pay.outcomes.model.request.TYPE_SIGN
import com.oplus.pay.outcomes.model.request.TYPE_SIGN_AND_PAY
import com.oplus.pay.outcomes.observer.FastPayObserver
import com.oplus.pay.outcomes.observer.MarketingObserver
import com.oplus.pay.outcomes.observer.OpenFingerPayObserver
import com.oplus.pay.outcomes.observer.PasterObserver
import com.oplus.pay.outcomes.statistic.StaticHelper
import com.oplus.pay.outcomes.ui.viewmodel.PayResultViewModel
import com.oplus.pay.settings.SettingHelper
import com.oplus.pay.ui.BaseActivity
import com.oplus.pay.ui.util.TipsHelper
import com.oplus.pay.ui.util.singleClick
import com.platform.usercenter.trace.rumtime.AutoTrace
import com.usercenter.custom.trace.OutcomesSceneTechTrace
import com.usercenter.custom.trace.OutcomesSceneTrace
import java.lang.ref.SoftReference
import java.math.BigDecimal


/**
 *
 * <p>Title: PayResultActivity</p>
 * <p>Description: PayResultActivity</p>
 * <p>Copyright (c) 2021 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR> by 80254882 on 2021/5/12.
 */
class OutcomesActivity : BaseActivity() {

    companion object {
        private const val TAG = "OutcomesActivity"

        fun launch(activity: Activity, bundle: Bundle) {
            Intent(activity, OutcomesActivity::class.java).apply {
                putExtras(bundle)
                activity.startActivity(this)
            }
        }
    }

    private lateinit var binding: OpayFtOutcomesActivityOutcomesBinding
    private val payResultViewModel by viewModels<PayResultViewModel>()
    private lateinit var outcomesParam: OutcomesParam
    private var mResult = STATUS_INIT
    private lateinit var outcomesPrePayResponse: OutcomesPrePayResponse

    // 手势检测相关变量
    private var startX = 0f
    private var startY = 0f
    private var isSwipeGesture = false
    private val touchSlop by lazy { ViewConfiguration.get(this).scaledTouchSlop }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        PayLogUtil.i(TAG, "OutcomesActivity onCreate is doing")
        StatusBarHelper.setStatusBarTransparentAndBlackFont(
            this,
            com.oplus.pay.ui.R.color.opay_lib_ui_bg_window
        )
        binding = OpayFtOutcomesActivityOutcomesBinding.inflate(layoutInflater)
        setContentView(binding.root)

        intent.getStringExtra(OutcomesRouter.EXTRA_OPEN_CHANNEL_PARAMS)?.apply {
            outcomesParam = OutcomesParam.fromJson(this)!!
            OutcomesHelper.notifyOutComeCreateDone(outcomesParam)
            initView()
            initOpenFingerPayObserver()
            initPaster()
            initMarketing()
            initListener()
            initObserve()
            initData()
        }
    }
    private fun initOpenFingerPayObserver() {
        lifecycle.addObserver(
            OpenFingerPayObserver(
                SoftReference(this),
                outcomesParam,
                payResultViewModel
            )
        )
    }

    private fun initPaster() {
        lifecycle.addObserver(PasterObserver(this, payResultViewModel, outcomesParam) {
            notifyResult()
        })
    }

    private fun initMarketing() {
        lifecycle.addObserver(
            MarketingObserver(
                this,
                SoftReference(binding.marketingContainer),
                payResultViewModel,
                outcomesParam
            )
        )
    }

    /**
     * 控件初始化
     */
    private fun initView() {
        binding.praResult?.startAnim(R.string.paying_with_verify)
        binding.bottomButtonOneContainer?.btnBottom?.setText(R.string.pay_result_button_confirm)
        binding.bottomButtonTwoContainer?.btnBottomRetry?.setText(R.string.query_again)
        binding.bottomButtonTwoContainer?.btnBottomCancel?.setText(com.oplus.pay.ui.R.string.sure_quit)
        SingleButtonWrap(
            binding.bottomButtonOneContainer.btnBottom,
            SingleButtonWrap.Type.SingleCentralLarge
        )

        binding.tvViewOrder.apply {
            COUITextViewCompatUtil.setPressRippleDrawable(this)
        }

        // 查看订单按钮点击事件
        binding.tvViewOrder?.singleClick {
            navigateToBillDetail()
            val resultCode = if (mResult == STATUS_OK) {
                OutcomesCode.CODE_SUCCESS.code.toString()
            } else {
                OutcomesCode.CODE_UNKNOWN.code.toString()
            }
            payResultViewModel.eventIdPayResultOrderDetail(outcomesParam,
                outcomesPrePayResponse,
                resultCode)
        }

        // 极速支付底部推荐位
        binding.rbBottomRecommend?.setOnCheckedChangeListener { _, isChecked ->
            AutoTrace.get().upload(
                OutcomesSceneTrace.eventIdPayresultPaytypeClick(
                    token = outcomesParam.bizExt.processToken,
                    order = outcomesParam.bizExt.partnerOrder,
                    partnerId = outcomesParam.bizExt.partnerCode,
                    countryCode = outcomesParam.bizExt.countryCode,
                    currencyCode = outcomesParam.bizExt.currency ?: "",
                    amount = outcomesParam.bizExt.actualAmount,
                    source = outcomesParam.bizExt.source,
                    packageName = packageName,
                    productName = outcomesParam.bizExt.productName ?: "",
                    isLogin = if (!TextUtils.isEmpty(outcomesParam.bizExt.processToken)) "1" else "0",
                    screenType = outcomesParam.bizExt.screenType,
                    prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                    isPrePay = if (TextUtils.isEmpty(outcomesParam.bizExt.prePayToken)) {
                        "0"
                    } else {
                        "1"
                    },
                    defaultPaytype = outcomesParam.channelId,
                    action = if (isChecked) "1" else "2",
                    DefaultPay = outcomesParam.bizExt.defaultPayType
                )
            )

            SettingHelper.updateDefaultPayType(
                processToken = outcomesParam.bizExt.processToken,
                defaultPayType = outcomesParam.channelId,
                status = if (isChecked) FastPayStatus.OPEN.type else FastPayStatus.CLOSE.type,
                country = outcomesParam.bizExt.countryCode
            ).observe(this) {
                when (it.status) {
                    Status.SUCCESS -> {
                        AutoTrace.get().upload(
                            OutcomesSceneTrace.eventIdPayresultPaytypeStatus(
                                token = outcomesParam.bizExt.processToken,
                                order = outcomesParam.bizExt.partnerOrder,
                                partnerId = outcomesParam.bizExt.partnerCode,
                                countryCode = outcomesParam.bizExt.countryCode,
                                currencyCode = outcomesParam.bizExt.currency ?: "",
                                amount = outcomesParam.bizExt.actualAmount,
                                source = outcomesParam.bizExt.source,
                                packageName = packageName,
                                productName = outcomesParam.bizExt.productName ?: "",
                                isLogin = if (!TextUtils.isEmpty(outcomesParam.bizExt.processToken)) "1" else "0",
                                screenType = outcomesParam.bizExt.screenType,
                                prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                                isPrePay = if (TextUtils.isEmpty(outcomesParam.bizExt.prePayToken)) {
                                    "0"
                                } else {
                                    "1"
                                },
                                defaultPaytype = outcomesParam.channelId,
                                action = if (isChecked) "1" else "2",
                                resultId = "success",
                                DefaultPay = outcomesParam.bizExt.defaultPayType
                            )
                        )
                    }

                    Status.ERROR -> {
                        AutoTrace.get().upload(
                            OutcomesSceneTrace.eventIdPayresultPaytypeStatus(
                                token = outcomesParam.bizExt.processToken,
                                order = outcomesParam.bizExt.partnerOrder,
                                partnerId = outcomesParam.bizExt.partnerCode,
                                countryCode = outcomesParam.bizExt.countryCode,
                                currencyCode = outcomesParam.bizExt.currency ?: "",
                                amount = outcomesParam.bizExt.actualAmount,
                                source = outcomesParam.bizExt.source,
                                packageName = packageName,
                                productName = outcomesParam.bizExt.productName ?: "",
                                isLogin = if (!TextUtils.isEmpty(outcomesParam.bizExt.processToken)) "1" else "0",
                                screenType = outcomesParam.bizExt.screenType,
                                prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                                isPrePay = if (TextUtils.isEmpty(outcomesParam.bizExt.prePayToken)) {
                                    "0"
                                } else {
                                    "1"
                                },
                                defaultPaytype = outcomesParam.channelId,
                                action = if (isChecked) "1" else "2",
                                resultId = "${it.message}",
                                DefaultPay = outcomesParam.bizExt.defaultPayType
                            )
                        )
                    }

                    else -> {
                        // nothing
                    }

                }
            }
        }
    }

    private fun initListener() {
        binding.bottomButtonOneContainer?.btnBottom?.singleClick {
            //成功/重新支付
            notifyResult()
            outcomesParam.bizExt.apply {
                AutoTrace.get().upload(
                    OutcomesSceneTechTrace.payresultDoneBtn(
                        this.countryCode,
                        this.source,
                        this.partnerOrder,
                        this.processToken,
                        this.partnerCode
                    )
                )
            }

        }
        binding.bottomButtonTwoContainer?.btnBottomCancel?.singleClick {
            //取消
            notifyResult()
            outcomesParam.bizExt.apply {
                AutoTrace.get().upload(
                    OutcomesSceneTechTrace.payresultExitBtn(
                        this.countryCode,
                        this.source,
                        this.partnerOrder,
                        this.processToken,
                        this.partnerCode
                    )
                )
            }
        }
        binding.bottomButtonTwoContainer?.btnBottomRetry?.singleClick {
            //重试
            queryResult(outcomesParam)
            outcomesParam.bizExt.apply {
                AutoTrace.get().upload(
                    OutcomesSceneTechTrace.payresultRetryBtn(
                        this.countryCode,
                        this.source,
                        this.partnerOrder,
                        this.processToken,
                        this.partnerCode
                    )
                )
            }
        }
    }

    private fun initObserve() {
        /**
         * 预下单结果查询 普通支付 签约并支付 纯签约
         */
        payResultViewModel.outcomesPrePayResponse.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    PayLogUtil.i(
                        TAG,
                        "outcomesPrePayResponse orderStatus: ${it.data?.status} -- partnerOrder: ${it.data?.partnerOrder}"
                    )
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.SUCCESS.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0009.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultSuccess(buildCommonMap)
                    outcomesPrePayResponse = it.data ?: OutcomesPrePayResponse()
                    if (TYPE_SIGN == outcomesParam.transType) {
                        switchStatueRequest(
                            outcomesPrePayResponse.goodsDetail?.goodsSignStatus ?: "",
                            outcomesPrePay = outcomesPrePayResponse
                        )
                    } else {
                        switchStatueRequest(
                            outcomesPrePayResponse.status,
                            outcomesPrePay = outcomesPrePayResponse
                        )
                    }
                    payWithResultStatic(
                        GSON.toJson(outcomesPrePayResponse),
                        GSON.toJson(outcomesParam),
                        "${outcomesPrePayResponse.creditDetail?.creditCount ?: ""}"
                    )
                }

                Status.ERROR -> {
                    TipsHelper.showErrorTips(it.code, it.message, OutcomesRouter.CODE_LIST)
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.ERROR.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0010.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultError(buildCommonMap)
                    switchStatueRequest(mResult)
                    payWithResultStatic("${it.code}\t${it.message}", GSON.toJson(outcomesParam))
                }

                Status.LOADING -> {
                }
            }
        }
        /**
         * 结果查询 普通支付
         */
        payResultViewModel.outcomesResponse.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    PayLogUtil.i(
                        TAG,
                        "outcomesResponse orderStatus: ${it.data?.orderStatus} -- signStatus: ${it.data?.payType}"
                    )
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.SUCCESS.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0009.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultSuccess(buildCommonMap)
                    DiffUpgradeHelper.loadEnd("OutcomesActivity", hashCode())
                    val outcomesResponse = it.data ?: OutcomesResponse()
                    switchStatueRequest(outcomesResponse.orderStatus, outcomes = outcomesResponse)
                    payWithResultStatic(
                        GSON.toJson(outcomesResponse),
                        GSON.toJson(outcomesParam),
                        "${outcomesResponse.creditCount}"
                    )
                }

                Status.ERROR -> {
                    DiffUpgradeHelper.loadEnd("OutcomesActivity", hashCode())
                    TipsHelper.showErrorTips(it.code, it.message, OutcomesRouter.CODE_LIST)
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.ERROR.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0010.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultError(buildCommonMap)
                    switchStatueRequest(mResult)
                    payWithResultStatic("${it.code}\t${it.message}", GSON.toJson(outcomesParam))
                }

                Status.LOADING -> {
                }
            }
        }
        /**
         * 结果查询 签约或签约并支付
         */
        payResultViewModel.outcomesSignResponse.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    DiffUpgradeHelper.loadEnd("OutcomesActivity", hashCode())
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.SUCCESS.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0009.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultSuccess(buildCommonMap)
                    val outcomesSignResponse = it.data ?: OutcomesSignResponse()
                    PayLogUtil.i(
                        TAG,
                        "outcomesSignResponse orderStatus: $outcomesSignResponse.orderStatus -- signStatus: ${outcomesSignResponse.signStatus}"
                    )
                    if (TYPE_SIGN == outcomesParam.transType) {
                        //纯签约
                        switchStatueRequest(
                            outcomesSignResponse.signStatus,
                            outcomesSign = outcomesSignResponse
                        )
                    } else {
                        //支付并签约
                        switchStatueRequest(
                            outcomesSignResponse.orderStatus,
                            outcomesSign = outcomesSignResponse
                        )
                    }
                    payWithResultStatic(
                        GSON.toJson(outcomesSignResponse),
                        GSON.toJson(outcomesParam)
                    )
                }

                Status.ERROR -> {
                    DiffUpgradeHelper.loadEnd("OutcomesActivity", hashCode())
                    TipsHelper.showErrorTips(it.code, it.message, OutcomesRouter.CODE_LIST)
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.ERROR.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0010.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultError(buildCommonMap)
                    switchStatueRequest(mResult)
                    payWithResultStatic("${it.code}\t${it.message}", GSON.toJson(outcomesParam))
                }

                Status.LOADING -> {
                }
            }
        }
        /**
         * 极速支付推荐信息
         */
        val fastPayObserver = FastPayObserver(payResultViewModel, outcomesParam)
        lifecycle.addObserver(fastPayObserver)
        fastPayObserver.asLiveData().observe(this) { recommend ->
            binding.rbBottomRecommend?.visibility = if (TextUtils.isEmpty(recommend.text)) {
                View.GONE
            } else {
                AutoTrace.get().upload(
                    OutcomesSceneTrace.eventIdPayresultPaytypeShow(
                        token = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        partnerId = outcomesParam.bizExt.partnerCode,
                        countryCode = outcomesParam.bizExt.countryCode,
                        currencyCode = outcomesParam.bizExt.currency ?: "",
                        amount = outcomesParam.bizExt.actualAmount,
                        source = outcomesParam.bizExt.source,
                        packageName = packageName,
                        productName = outcomesParam.bizExt.productName ?: "",
                        isLogin = if (!TextUtils.isEmpty(outcomesParam.bizExt.processToken)) "1" else "0",
                        screenType = outcomesParam.bizExt.screenType,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        isPrePay = if (TextUtils.isEmpty(outcomesParam.bizExt.prePayToken)) {
                            "0"
                        } else {
                            "1"
                        },
                        defaultPaytype = outcomesParam.channelId,
                        DefaultPay = outcomesParam.bizExt.defaultPayType
                    )
                )

                View.VISIBLE
            }
            binding.rbBottomRecommend?.text = Html.fromHtml(recommend.text)
            binding.rbBottomRecommend?.isChecked = recommend.status
        }

    }

    /**
     * 单按钮、双按钮切换
     */
    private fun changeButtonAreaVisibility(buttonOne: Int, buttonTwo: Int) {
        binding.bottomButtonOneContainer?.root?.visibility = buttonOne
        binding.bottomButtonTwoContainer?.root?.visibility = buttonTwo
    }

    /**
     * 数据初始化
     * 发送支付状态查询、营销位请求
     */
    private fun initData() {
        DiffUpgradeHelper.loadStart("OutcomesActivity", hashCode())
        val buildCommonMap = StaticHelper.buildCommonMap(
            processToken = outcomesParam.bizExt.processToken,
            order = outcomesParam.bizExt.partnerOrder,
            prePayToken = outcomesParam.bizExt.prePayToken ?: "",
            bizNode = BizNode.TRANSACTION_RESULT.value,
            orderType = outcomesParam.bizExt.contractType,
            bizResult = BizResult.SUCCESS.value,
            bizCode = TransactionResultStatusCodes.CODE_05_000_0008.statusCode,
            outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                put("channelId", outcomesParam.channelId)
                put("payOrder", outcomesParam.payOrder)
                put("transType", "${outcomesParam.transType}")
                put("isAcrossType", "false")
            })
        )
        StaticHelper.eventIdQueryResultStart(buildCommonMap)
        queryResult(outcomesParam)
    }

    /**
     * 结果查询 支付
     */
    private fun queryResult(resultParam: OutcomesParam) {
        if (!TextUtils.isEmpty(resultParam.bizExt.prePayToken)) {
            payResultViewModel.outComesPrePayParam.value = resultParam
        } else {
            resultParam.transType?.apply {
                if (TransType.PAY.type == this || TransType.PAYMENT.type == this) {
                    payResultViewModel.outComesParam.value = resultParam
                } else {
                    payResultViewModel.outComesSingParam.value = resultParam
                }
            } ?: kotlin.run {
                payResultViewModel.outComesParam.value = resultParam
            }
        }
    }

    /**
     * 切换结果查询状态
     */
    private fun switchStatueRequest(
        status: String,
        outcomes: OutcomesResponse? = null,
        outcomesSign: OutcomesSignResponse? = null,
        outcomesPrePay: OutcomesPrePayResponse? = null
    ) {
        when (status) {
            STATUS_INIT -> {
                mResult = STATUS_INIT
                onResultInit()
            }

            STATUS_FAIL -> {
                mResult = STATUS_FAIL
                onResultFail(outcomesPrePay)
            }

            STATUS_OK -> {
                mResult = STATUS_OK
                payResultViewModel.paySuccess.value = true
                //普通支付
                if (outcomes != null) {
                    onPayResultOk(outcomes)
                }
                if (outcomesPrePay != null) {
                    if (TYPE_SIGN != outcomesParam.transType && TYPE_SIGN_AND_PAY != outcomesParam.transType) {
                        onPrePayResultOk(outcomesPrePay)
                    } else {
                        onPrePaySignResultOk(outcomesPrePay)
                    }
                }
                //签约、签约并支付
                if (outcomesSign != null) {
                    onSignResultOk(outcomesSign)
                }
            }
        }
    }

    /**
     * 支付结果状态详情更新
     */
    @SuppressLint("SetTextI18n")
    private fun updateStatusDetail(
        amount: String,
        productsName: String,
        channelPromotionText: String? = null
    ) {
        // 实付金额
        setRealAmount(amount)
        // 商品名称
        setProductInfo(productsName)

        channelPromotionText?.let {
            setchannelPromotiontext(it)
        }

        changeButtonAreaVisibility(View.VISIBLE, View.GONE)
        binding.bottomButtonOneContainer?.btnBottom?.isEnabled = true
    }

    private fun setProductInfo(productsName: String?) {
        if (productsName.isNullOrEmpty()) {
            binding.llProduct?.visibility = View.GONE
        } else {
            binding.llProduct?.visibility = View.VISIBLE
            binding.tvProduct?.text = productsName
        }

    }

    private fun setRealAmount(amount: String?) {
        if (amount.isNullOrEmpty()) {
            binding.llAccount?.visibility = View.GONE
        } else {
            binding.llAccount?.visibility = View.VISIBLE
            val realAmountBd = CurrencyHelper.fenToYuan(amount)
            if (realAmountBd.compareTo(BigDecimal.ZERO) == 0) {
                //当虚拟资产完全抵扣商品金额时，即付款金额为0，此处商品实付金额不展示
                binding.llAccount?.visibility = View.GONE
                return
            }
            val realAmount = realAmountBd.toFloat()
            if (BizHelper.isFlavorChina) {
                binding.tvActualPay?.visibility = View.VISIBLE
                binding.tvUnit?.visibility = View.GONE
                val spannableString =
                    SpannableString(getString(R.string.real_pay_value, realAmount.toString()))
                val start = spannableString.indexOf(getString(R.string.real_pay_value, ""))
                spannableString.setSpan(
                    AbsoluteSizeSpan(16, true),
                    start,
                    start + getString(R.string.real_pay_value, "").length,
                    Spanned.SPAN_INCLUSIVE_INCLUSIVE
                )
                binding.tvAccount?.text = spannableString
            } else {
                binding.tvActualPay?.visibility = View.VISIBLE
                binding.tvAccount?.text =
                    CurrencyHelper.strToBigDecimal(realAmount.toString()).toPlainString()
                binding.tvUnit?.text = outcomesParam.bizExt.currency ?: ""
            }

        }

    }

    /**
     * 处理支付结果成功的状态
     */
    private fun onPrePayResultOk(outcomes: OutcomesPrePayResponse) {
        //默认展示"支付成功"
        var payResultExpand = R.string.pay_result_expand_success
        //当notifyStatus为OK时展示"支付成功，已通知商户发货"
        outcomes.notifyStatus?.let {
            if (it == NOTIFY_STATUS_OK) {
                payResultExpand = R.string.pay_result_expand_success_notify_merchant_ship
            }
        }
        binding.praResult?.showResult(
            R.drawable.opay_ft_outcomes_success_icon,
            payResultExpand,
            PayResultArea.STATE_SUCCESS
        )
        binding.prdInfoLayout?.visibility = View.VISIBLE
        setStateTextViewColorAndSize(R.color.opay_ft_outcomes_pay_result_state, 20f)
        updateStatusDetail(
            outcomes.payAmount.toString(),
            outcomes.goodsDetail?.goodsName ?: "",
            outcomes.channelPromotionText
        )
        if (BizHelper.isFlavorChina) {
            // 显示查看订单按钮
            binding.tvViewOrder?.visibility = View.VISIBLE
        }
        payResultViewModel.showFreePassTips(outcomesParam, outcomes)
        //技术埋点，成状态下，统计支付成功和支付成功已联系商户发货的量
        outcomes.apply {
            AutoTrace.get().upload(
                OutcomesSceneTechTrace.eventIdPrePayResultOk(
                    notifyStatus = this.notifyStatus,
                    statusDesc = getString(payResultExpand),
                    countryCode = outcomesParam.bizExt.countryCode,
                    source = outcomesParam.bizExt.source,
                    order = outcomesParam.bizExt.partnerOrder,
                    token = outcomesParam.bizExt.processToken,
                    partnerId = outcomesParam.bizExt.partnerCode
                )
            )
        }
    }

    private fun setchannelPromotiontext(channelPromotionText: String) {
        if(channelPromotionText.isNullOrEmpty()) {
            binding.llPromotiont.visibility = View.GONE
            binding.tvChannelPromotiontDesc.visibility = View.GONE
            return
        }
        binding.tvChannelPromotiontDesc.text = channelPromotionText
        binding.llPromotiont.visibility = View.VISIBLE
        binding.tvChannelPromotiontDesc.visibility = View.VISIBLE
    }


    /**
     * 处理支付结果成功的状态
     */
    private fun onPayResultOk(outcomes: OutcomesResponse) {
        binding.praResult?.showResult(
            R.drawable.opay_ft_outcomes_success_icon,
            R.string.pay_result_expand_success,
            PayResultArea.STATE_SUCCESS
        )
        binding.prdInfoLayout?.visibility = View.VISIBLE
        setStateTextViewColorAndSize(R.color.opay_ft_outcomes_pay_result_state, 20f)
        updateStatusDetail(
            outcomes.amount.toString(),
            outcomes.productsName,
            outcomes.channelPromotionText
        )
        if (BizHelper.isFlavorChina) {
            // 显示查看订单按钮
            binding.tvViewOrder?.visibility = View.VISIBLE
        }
        payResultViewModel.showFreePassTips(outcomesParam, outcomes)
    }

    /**
     * 处理支付结果成功的状态
     */
    private fun onPrePaySignResultOk(outcomes: OutcomesPrePayResponse) {
        if (TYPE_SIGN == outcomesParam.transType) {
            //签约并支付 虚拟资产抵扣后交易类型是"SIGN"，用商品价格区分
            if (outcomes.goodsDetail?.goodsPrice == 0) {
                binding.tvViewOrder?.visibility = View.GONE
            } else {
                if (BizHelper.isFlavorChina) {
                    binding.tvViewOrder?.visibility = View.VISIBLE
                }
            }
            binding.praResult?.showResult(
                R.drawable.opay_ft_outcomes_success_icon,
                R.string.pay_result_open_success,
                PayResultArea.STATE_SUCCESS
            )
        } else {
            if (BizHelper.isFlavorChina) {
                // 显示查看订单按钮
                binding.tvViewOrder?.visibility = View.VISIBLE
            }
            binding.praResult?.showResult(
                R.drawable.opay_ft_outcomes_success_icon,
                R.string.pay_result_expand_success,
                PayResultArea.STATE_SUCCESS
            )
        }
        binding.prdInfoLayout?.visibility = View.VISIBLE
        setStateTextViewColorAndSize(R.color.opay_ft_outcomes_pay_result_state, 20f)
        updateStatusDetail(
            outcomes.payAmount.toString(),
            outcomes.goodsDetail?.goodsName ?: "",
            outcomes.channelPromotionText
        )
        if (!outcomes.freePwdDetail?.channelUserLoginId.isNullOrEmpty()) {
            if (BizHelper.isFlavorChina) {
                return
            }
            ToastUtil.show(
                AppRuntime.getAppContext().getString(
                    R.string.pay_result_open_free_pass_sccuess_msg,
                    outcomesParam.bizExt.channelName,
                    outcomes.freePwdDetail?.channelUserLoginId
                )
            )
        }
        if (!outcomes.channelUserLoginId.isNullOrEmpty()) {
            if (BizHelper.isFlavorChina) {
                return
            }
            ToastUtil.show(
                AppRuntime.getAppContext().getString(
                    R.string.pay_result_open_free_pass_sccuess_msg,
                    outcomesParam.bizExt.channelName,
                    outcomes.channelUserLoginId
                )
            )
        }
    }

    /**
     * 处理支付结果成功的状态
     */
    private fun onSignResultOk(outcomes: OutcomesSignResponse) {
        if (TYPE_SIGN == outcomesParam.transType) {
            // 纯签约隐藏查看订单按钮
            binding.tvViewOrder?.visibility = View.GONE
            binding.praResult?.showResult(
                R.drawable.opay_ft_outcomes_success_icon,
                R.string.pay_result_open_success,
                PayResultArea.STATE_SUCCESS
            )
        } else {
            if (BizHelper.isFlavorChina) {
                // 显示查看订单按钮
                binding.tvViewOrder?.visibility = View.VISIBLE
            }
            binding.praResult?.showResult(
                R.drawable.opay_ft_outcomes_success_icon,
                R.string.pay_result_expand_success,
                PayResultArea.STATE_SUCCESS
            )
        }
        binding.prdInfoLayout?.visibility = View.VISIBLE
        setStateTextViewColorAndSize(R.color.opay_ft_outcomes_pay_result_state, 20f)
        updateStatusDetail(outcomes.amount.toString(), outcomes.productsName)
        if (!outcomes.channelUserLoginId.isNullOrEmpty()) {
            if (BizHelper.isFlavorChina) {
                return
            }
            ToastUtil.show(
                AppRuntime.getAppContext().getString(
                    R.string.pay_result_open_free_pass_sccuess_msg,
                    outcomesParam.bizExt.channelName,
                    outcomes.channelUserLoginId
                )
            )
        }
    }

    /**
     * 处理支付结果失败的状态
     */
    private fun onResultFail(outcomes: OutcomesPrePayResponse?) {
        val showTipStrId =
            if (TYPE_SIGN == outcomesParam.transType) R.string.pay_result_only_sign_fail else R.string.pay_result_expand_fail
        if (TYPE_SIGN == outcomesParam.transType) {
            outcomes?.let {
                //签约并支付 虚拟资产抵扣后交易类型是"SIGN"，用商品价格区分
                if (it.goodsDetail?.goodsPrice == 0) {
                    binding.tvViewOrder?.visibility = View.GONE
                } else {
                    if (BizHelper.isFlavorChina) {
                        binding.tvViewOrder?.visibility = View.VISIBLE
                    }
                }
            }
        } else {
            if (BizHelper.isFlavorChina) {
                binding.tvViewOrder?.visibility = View.VISIBLE
            }
        }
        binding.praResult?.showResult(
            com.oplus.pay.ui.R.drawable.opay_lib_ui_fail_icon,
            showTipStrId,
            PayResultArea.STATE_FAIL
        )
        binding.prdInfoLayout?.visibility = View.VISIBLE
        setStateTextViewColorAndSize(R.color.opay_ft_outcomes_pay_result_state, 20f)
        updateStatusDetail("", "")
        binding.bottomButtonOneContainer?.btnBottom?.isEnabled = true
    }


    private fun setStateTextViewColorAndSize(color: Int, size: Float) {
        //设置字体大小和颜色
        resources?.let {
            binding.praResult?.setTextStateViewTextColor(it.getColor(color))
        }
        binding.praResult?.setTextStateViewTextSize(size)
    }

    /**
     * 处理支付结果未知的状态
     */
    private fun onResultInit() {
        val realReceiveId =
            if (TYPE_SIGN == outcomesParam.transType) R.string.pay_result_auto_renew_sub_ongoning_msg else R.string.receive_result_need
        binding.praResult?.showResult(
            R.drawable.opay_ft_outcomes_noresult_icon,
            realReceiveId,
            PayResultArea.STATE_NO_RESULT
        )
        binding.prdInfoLayout?.visibility = View.GONE
        // 隐藏查看订单按钮
        binding.tvViewOrder?.visibility = View.GONE
        setStateTextViewColorAndSize(com.oplus.pay.ui.R.color.opay_lib_ui_color_black_alpha_45, 12f)

        // 如果此时距离进入结果页面不满一秒钟，则不显示"重试/退出"按钮。
        val timeDiff = System.currentTimeMillis() - payResultViewModel.enterTime
        changeButtonAreaVisibility(View.GONE, if (timeDiff < 1000) View.GONE else View.VISIBLE)

        payResultViewModel.polling {
            queryResult(outcomesParam)
        }
    }

    /**
     * 跳转到订单详情页面
     */
    private fun navigateToBillDetail() {
        com.oplus.pay.subscription.PaySubscriptionHelper.launchBillDetail(
            context = this,
            processToken = outcomesParam.bizExt.processToken,
            bizExt = outcomesParam.bizExt,
            payOrder = outcomesParam.payOrder,
            paymentCode = outcomesPrePayResponse.paymentCode,
            paymentName = outcomesPrePayResponse.paymentName,
            appName = outcomesPrePayResponse.appName,
            payAmount = outcomesPrePayResponse.payAmount.toString(),
            originalAmount = outcomesPrePayResponse.originalAmount.toString(),
            productName = outcomesParam.bizExt.productName,
            paymentResult = when (mResult) {
                STATUS_OK -> "SUCCESS"
                STATUS_FAIL -> "FAILURE"
                else -> "FAILURE"
            },
            transactionStatusDesc = when (mResult) {
                STATUS_OK -> getString(R.string.pay_result_expand_success)
                STATUS_FAIL -> getString(R.string.pay_result_expand_fail)
                else -> getString(R.string.pay_result_expand_fail)
            },
            detailIcon = outcomesPrePayResponse.detailIcon,
            transactionDateTime = if (STATUS_OK == mResult) outcomesPrePayResponse.successTime else outcomesPrePayResponse.requestTime,
            orderAmount = outcomesPrePayResponse.originalAmount.toString(),
            promotionDiscountAmount = if (STATUS_OK == mResult) outcomesPrePayResponse.promotionDiscountAmount.toString() else "0",
            keBiDeductAmount = if (STATUS_OK == mResult) {
                if (outcomesPrePayResponse.cocoinDetail != null) outcomesPrePayResponse.cocoinDetail?.cocoinDeducAmount.toString() else "0"
            } else {
                "0"
            },
            voucherDeductAmount = if (STATUS_OK == mResult) outcomesPrePayResponse.voucherAmount.toString() else "0",
            creditDeductAmount = if (STATUS_OK == mResult) {
                if (outcomesPrePayResponse.creditDetail != null) outcomesPrePayResponse.creditDetail?.creditDeductAmount.toString() else "0"
            } else {
                "0"
            }
        )
    }

    /**
     * 发送广播
     */
    private fun notifyResult() {
        when (mResult) {
            STATUS_INIT -> {
                OutcomesHelper.notifyUnknown(outcomesParam)
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = outcomesParam.bizExt.processToken,
                    order = outcomesParam.bizExt.partnerOrder,
                    prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                    bizNode = BizNode.TRANSACTION_RESULT.value,
                    orderType = outcomesParam.bizExt.contractType,
                    bizResult = BizResult.ERROR.value,
                    bizCode = TransactionResultStatusCodes.CODE_05_000_0002.statusCode,
                    outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                        put("channelId", outcomesParam.channelId)
                        put("payOrder", outcomesParam.payOrder)
                        put("transType", "${outcomesParam.transType}")
                        put("isAcrossType", "false")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdTransactionResult(buildCommonMap)
            }

            STATUS_FAIL -> {
                // 重新支付
                OutcomesHelper.notifyUnknown(outcomesParam)
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = outcomesParam.bizExt.processToken,
                    order = outcomesParam.bizExt.partnerOrder,
                    prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                    bizNode = BizNode.TRANSACTION_RESULT.value,
                    orderType = outcomesParam.bizExt.contractType,
                    bizResult = BizResult.ERROR.value,
                    bizCode = TransactionResultStatusCodes.CODE_05_000_0001.statusCode,
                    outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                        put("channelId", outcomesParam.channelId)
                        put("payOrder", outcomesParam.payOrder)
                        put("transType", "${outcomesParam.transType}")
                        put("isAcrossType", "false")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdTransactionResult(buildCommonMap)
            }

            STATUS_OK -> {
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = outcomesParam.bizExt.processToken,
                    order = outcomesParam.bizExt.partnerOrder,
                    prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                    bizNode = BizNode.TRANSACTION_RESULT.value,
                    orderType = outcomesParam.bizExt.contractType,
                    bizResult = BizResult.SUCCESS.value,
                    bizCode = TransactionResultStatusCodes.CODE_05_000_0000.statusCode,
                    outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                        put("channelId", outcomesParam.channelId)
                        put("payOrder", outcomesParam.payOrder)
                        put("transType", "${outcomesParam.transType}")
                        put("isAcrossType", "false")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdTransactionResult(buildCommonMap)
                // 通知业务方支付结果
                OutcomesHelper.notifySuccess(outcomesParam)
            }
        }
        finish()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            logBackAction("PHYSICAL_BACK_KEY")
            true
        } else super.onKeyDown(keyCode, event)
    }

    override fun onBackPressed() {
        // 拦截系统返回键（包括导航栏返回键）
        logBackAction("SYSTEM_BACK_PRESSED")
        // 不调用super.onBackPressed()来阻止默认返回行为
    }

    /**
     * 记录返回操作埋点
     */
    private fun logBackAction(actionType: String) {
        // 使用OutcomesSceneTrace.eventIdPayResultBack进行埋点上报
        outcomesParam.bizExt.apply {
            AutoTrace.get().upload(
                OutcomesSceneTrace.eventIdPayResultBack(
                    token = this.processToken,
                    order = this.partnerOrder,
                    appVersion = BizHelper.getAppVersion(),
                    screenType = this.screenType,
                    keyType = actionType
                )
            )
        }
    }

    /**
     * 带支付结果的埋点上报
     * @param creditNum 抵扣积分
     * @param result 支付结果
     */
    private fun payWithResultStatic(result: String, request: String, creditNum: String = "") {
        outcomesParam.apply {
            //支付结果页面自带结果曝光
            AutoTrace.get().upload(
                OutcomesSceneTrace.payresultShow(
                    request = request,
                    countryCode = this.bizExt.countryCode,
                    source = this.bizExt.source,
                    partnerId = this.bizExt.partnerCode,
                    payType = this.channelId,
                    payResult = result,
                    order = this.bizExt.partnerOrder,
                    token = this.bizExt.processToken,
                    amount = "${bizExt.actualAmount}",
                    packageName = appPackage,
                    productName = bizExt.productName ?: "",
                    screenType = bizExt.screenType
                )
            )
            // 支付结果页面查询结果
            AutoTrace.get().upload(
                OutcomesSceneTrace.payresultRequest(
                    request = request,
                    creditNum = creditNum,
                    screenType = this.bizExt.screenType,
                    source = this.bizExt.source,
                    payResult = result,
                    token = this.bizExt.processToken,
                    countryCode = this.bizExt.countryCode,
                    partnerId = this.bizExt.partnerCode,
                    payType = this.channelId,
                    order = this.bizExt.partnerOrder
                )
            )

            this.bizExt.apply {
                AutoTrace.get().upload(
                    OutcomesSceneTechTrace.payresultShowTech(
                        request = request,
                        creditNum = creditNum,
                        screenType = screenType,
                        source = this.source,
                        payResult = result,
                        token = this.processToken,
                        countryCode = this.countryCode,
                        partnerId = this.partnerCode,
                        payType = outcomesParam.channelId,
                        transType = outcomesParam.transType ?: "",
                        order = this.partnerOrder
                    )
                )
            }

        }
    }
}
