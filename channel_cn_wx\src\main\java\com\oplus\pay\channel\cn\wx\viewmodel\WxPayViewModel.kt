package com.oplus.pay.channel.cn.wx.viewmodel

import android.app.Activity
import android.content.BroadcastReceiver
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.basic.Resource
import com.oplus.pay.basic.Status
import com.oplus.pay.channel.cn.wx.usecase.WxPayUseCase
import com.oplus.pay.channel.model.request.OpenChannelParams
import com.oplus.pay.outcomes.QueryChannelStatusParam

/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright (c) 2020 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR>
 * @version 1.0
 */

private const val TAG = "WxPayViewModel"

class WxPayViewModel : ViewModel()  {
    /**
     * 拉起支付渠道的参数
     */
    val openChannelParams: MutableLiveData<OpenChannelParams?> = MutableLiveData(null)

    /**
     * 标记是否是重渠道返回
     */
    val onPause = MutableLiveData(false)
    val openChannel = MutableLiveData(false)

    /**
     * 渠道发送支付结果广播 用于跳转结果页
     * @param openChannelParams 全局缓存的的下单后拉起渠道的参数
     * @param userCancel 是否是用户主动取消，主动取消需要解注册广播，避免内存泄漏
     */
    fun notifyPayResult(openChannelParams: String? = null, userCancel: Boolean = false, actionStr: String, errorCode: Int, errorMsg: String) {
        WxPayUseCase.notifyPayResult(openChannelParams, userCancel, actionStr, errorCode, errorMsg)
    }

    fun registerOutComeReceiver(activity: Activity, outComesReceiver: BroadcastReceiver) {
        WxPayUseCase.registerOutComeReceiver(activity, outComesReceiver)
    }

    fun unRegisterOutComeReceiver(activity: Activity, outComesReceiver: BroadcastReceiver) {
        WxPayUseCase.unRegisterOutComeReceiver(activity, outComesReceiver)
    }

}