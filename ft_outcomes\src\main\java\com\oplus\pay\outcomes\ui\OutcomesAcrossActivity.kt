package com.oplus.pay.outcomes.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Html
import android.text.TextUtils
import android.view.*
import android.widget.CheckBox
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.viewModels
import androidx.cardview.widget.CardView
import androidx.core.widget.NestedScrollView
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.button.SingleButtonWrap
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.oplus.pay.basic.AppRuntime
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.basic.Status
import com.oplus.pay.basic.util.serialize.GSON
import com.oplus.pay.basic.util.ui.*
import com.oplus.pay.biz.BizHelper
import com.oplus.pay.biz.FastPayStatus
import com.oplus.pay.biz.TransType
import com.oplus.pay.diff.DiffUpgradeHelper
import com.oplus.pay.marketing.model.response.*
import com.oplus.pay.monitor.api.d
import com.oplus.pay.monitor.api.model.*
import com.oplus.pay.outcomes.*
import com.oplus.pay.outcomes.api.OutcomesRouter
import com.oplus.pay.outcomes.model.*
import com.oplus.pay.outcomes.model.request.TYPE_SIGN
import com.oplus.pay.outcomes.model.request.TYPE_SIGN_AND_PAY
import com.oplus.pay.outcomes.observer.FastPayObserver
import com.oplus.pay.outcomes.observer.MarketingObserver
import com.oplus.pay.outcomes.observer.PasterObserver
import com.oplus.pay.outcomes.statistic.StaticHelper
import com.oplus.pay.outcomes.ui.viewmodel.PayResultViewModel
import com.oplus.pay.settings.SettingHelper
import com.oplus.pay.ui.BaseActivity
import com.oplus.pay.ui.util.TipsHelper
import com.oplus.pay.ui.util.UiHelper
import com.oplus.pay.ui.util.singleClick
import com.platform.usercenter.trace.rumtime.AutoTrace
import com.usercenter.custom.trace.OutcomesSceneTechTrace
import com.usercenter.custom.trace.OutcomesSceneTrace
import java.lang.ref.SoftReference
import java.util.*

/**
 * <p>Title: PayResultActivity</p>
 * <p>Description: PayResultActivity</p>
 * <p>Copyright (c) 2021 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR> by 80254882 on 2021/5/12.
 */
class OutcomesAcrossActivity : BaseActivity(), View.OnClickListener {

    companion object {
        private const val TAG = "OutcomesAcrossActivity"

        fun launch(activity: Activity, bundle: Bundle) {
            Intent(activity, OutcomesAcrossActivity::class.java).apply {
                putExtras(bundle)
                activity.startActivity(this)
            }
        }
    }

    private val payResultViewModel by viewModels<PayResultViewModel>()
    private lateinit var outcomesParam: OutcomesParam
    private var mResult = STATUS_INIT

    private lateinit var myCardView: CardView
    private lateinit var mPayResult: PayResultArea
    private lateinit var mMarketingContainer: LinearLayout
    private lateinit var mCompleteBtn: COUIButton
    private lateinit var mLeftBtn: COUIButton
    private lateinit var mRightBtn: COUIButton
    private lateinit var mScrollView: NestedScrollView
    private lateinit var mDivderLine: View
    private lateinit var mRecommondCheck: CheckBox
    private lateinit var mTwoBtnLayout: LinearLayout
    private lateinit var mViewOrderBtn: TextView
    private lateinit var outcomesPrePayResponse: OutcomesPrePayResponse

    // 手势检测相关变量
    private var startX = 0f
    private var startY = 0f
    private var isSwipeGesture = false
    private val touchSlop by lazy { ViewConfiguration.get(this).scaledTouchSlop }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            window.decorView.systemUiVisibility =
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        }
    }

    override fun isSwitchScreenByOS(): Boolean = false
    override fun onCreate(savedInstanceState: Bundle?) {
        PayLogUtil.i(TAG, "OutcomesAcrossActivity onCreate is doing")
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        window.setFlags(
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                    WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                    WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        super.onCreate(savedInstanceState)
        window.decorView.apply {
            systemUiVisibility =
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_FULLSCREEN
        }
        StatusBarHelper.setStatusBarTransparentAndBlackFont(this)
        if (BizHelper.isFlavorChina) {
            overridePendingTransition(0, 0)
            setContentView(R.layout.opay_ft_outcomes_activity_outcomes_across)
        } else {
            setContentView(R.layout.opay_ft_outcomes_activity_outcomes_os_across)
        }

        adapterNavigation()

        intent.getStringExtra(OutcomesRouter.EXTRA_OPEN_CHANNEL_PARAMS)?.apply {
            outcomesParam = OutcomesParam.fromJson(this)!!
            OutcomesHelper.notifyOutComeCreateDone(outcomesParam)
            initView()
            initPaster()
            initMarketing()
            initListener()
            initObserve()
            initData()
        }
        UiHelper.doShowAnimation(findViewById(R.id.myCardView))
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0)
    }

    override fun onResume() {
        super.onResume()
        overridePendingTransition(0, 0)
    }

    /**
     * 适配导航栏
     */
    private fun adapterNavigation() {
        if (PayNavigationUtils.isNeedAdapterNavigation(this)) {
            PayNavigationUtils.scrollPageNoNeedPadding(this)
        } else {
            //这句影响到导航栏适配，只能通过导航栏的方式区分处理
            StatusBarHelper.setStatusBarTransparentAndBlackFont(this)
        }
    }

    private fun initPaster() {
        lifecycle.addObserver(PasterObserver(this, payResultViewModel, outcomesParam) {
            notifyResult()
        })
    }

    private fun initMarketing() {
        lifecycle.addObserver(
            MarketingObserver(
                this,
                SoftReference(mMarketingContainer),
                payResultViewModel,
                outcomesParam
            )
        )
    }

    /**
     * 控件初始化
     */
    private fun initView() {
        myCardView = findViewById(R.id.myCardView)
        mPayResult = findViewById(R.id.pay_result)
        mMarketingContainer = findViewById(R.id.marketing_container)
        mCompleteBtn = findViewById(R.id.btn_complete)
        mLeftBtn = findViewById(R.id.btn_bottom_left)
        mRightBtn = findViewById(R.id.btn_bottom_right)
        mScrollView = findViewById(R.id.scrollView)
        mDivderLine = findViewById(R.id.divider_line)
        mRecommondCheck = findViewById(R.id.rb_bottom_recommend)
        mTwoBtnLayout = findViewById(R.id.layout_two_button)
        mViewOrderBtn = findViewById(R.id.tv_view_order)
        mPayResult.startAnim(R.string.paying_with_verify)

        mViewOrderBtn.apply {
            COUITextViewCompatUtil.setPressRippleDrawable(this)
        }

        // 查看订单按钮点击事件
        mViewOrderBtn.singleClick {
            navigateToBillDetail()
            val resultCode = if (mResult == STATUS_OK) {
                OutcomesCode.CODE_SUCCESS.code.toString()
            } else {
                OutcomesCode.CODE_UNKNOWN.code.toString()
            }
            payResultViewModel.eventIdPayResultOrderDetail(outcomesParam,
                outcomesPrePayResponse,
                resultCode)
        }
        mCompleteBtn.setText(R.string.pay_result_button_confirm)
        mRightBtn.setText(R.string.query_again)
        mLeftBtn.setText(com.oplus.pay.ui.R.string.sure_quit)
        SingleButtonWrap(mCompleteBtn, SingleButtonWrap.Type.SingleCentralLarge)

        setScrollChangeListener(mScrollView, mDivderLine)
        // 极速支付底部推荐位
        mRecommondCheck.setOnCheckedChangeListener { _, isChecked ->
            AutoTrace.get().upload(
                OutcomesSceneTrace.eventIdPayresultPaytypeClick(
                    token = outcomesParam.bizExt.processToken,
                    order = outcomesParam.bizExt.partnerOrder,
                    partnerId = outcomesParam.bizExt.partnerCode,
                    countryCode = outcomesParam.bizExt.countryCode,
                    currencyCode = outcomesParam.bizExt.currency ?: "",
                    amount = outcomesParam.bizExt.actualAmount,
                    source = outcomesParam.bizExt.source,
                    packageName = packageName,
                    productName = outcomesParam.bizExt.productName ?: "",
                    isLogin = if (!TextUtils.isEmpty(outcomesParam.bizExt.processToken)) "1" else "0",
                    screenType = outcomesParam.bizExt.screenType,
                    prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                    isPrePay = if (TextUtils.isEmpty(outcomesParam.bizExt.prePayToken)) {
                        "0"
                    } else {
                        "1"
                    },
                    defaultPaytype = outcomesParam.channelId,
                    action = if (isChecked) "1" else "2",
                    DefaultPay = outcomesParam.bizExt.defaultPayType
                )
            )
            SettingHelper.updateDefaultPayType(
                processToken = outcomesParam.bizExt.processToken,
                defaultPayType = outcomesParam.channelId,
                status = if (isChecked) FastPayStatus.OPEN.type else FastPayStatus.CLOSE.type,
                country = outcomesParam.bizExt.countryCode
            ).observe(this) {
                when (it.status) {
                    Status.SUCCESS -> {
                        AutoTrace.get().upload(
                            OutcomesSceneTrace.eventIdPayresultPaytypeStatus(
                                token = outcomesParam.bizExt.processToken,
                                order = outcomesParam.bizExt.partnerOrder,
                                partnerId = outcomesParam.bizExt.partnerCode,
                                countryCode = outcomesParam.bizExt.countryCode,
                                currencyCode = outcomesParam.bizExt.currency ?: "",
                                amount = outcomesParam.bizExt.actualAmount,
                                source = outcomesParam.bizExt.source,
                                packageName = packageName,
                                productName = outcomesParam.bizExt.productName ?: "",
                                isLogin = if (!TextUtils.isEmpty(outcomesParam.bizExt.processToken)) "1" else "0",
                                screenType = outcomesParam.bizExt.screenType,
                                prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                                isPrePay = if (TextUtils.isEmpty(outcomesParam.bizExt.prePayToken)) {
                                    "0"
                                } else {
                                    "1"
                                },
                                defaultPaytype = outcomesParam.channelId,
                                action = if (isChecked) "1" else "2",
                                resultId = "success",
                                DefaultPay = outcomesParam.bizExt.defaultPayType
                            )
                        )
                    }

                    Status.ERROR -> {
                        AutoTrace.get().upload(
                            OutcomesSceneTrace.eventIdPayresultPaytypeStatus(
                                token = outcomesParam.bizExt.processToken,
                                order = outcomesParam.bizExt.partnerOrder,
                                partnerId = outcomesParam.bizExt.partnerCode,
                                countryCode = outcomesParam.bizExt.countryCode,
                                currencyCode = outcomesParam.bizExt.currency ?: "",
                                amount = outcomesParam.bizExt.actualAmount,
                                source = outcomesParam.bizExt.source,
                                packageName = packageName,
                                productName = outcomesParam.bizExt.productName ?: "",
                                isLogin = if (!TextUtils.isEmpty(outcomesParam.bizExt.processToken)) "1" else "0",
                                screenType = outcomesParam.bizExt.screenType,
                                prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                                isPrePay = if (TextUtils.isEmpty(outcomesParam.bizExt.prePayToken)) {
                                    "0"
                                } else {
                                    "1"
                                },
                                defaultPaytype = outcomesParam.channelId,
                                action = if (isChecked) "1" else "2",
                                resultId = "${it.message}",
                                DefaultPay = outcomesParam.bizExt.defaultPayType
                            )
                        )
                    }

                    else -> {
                        // nothing
                    }
                }
            }
        }
    }

    private fun initListener() {
        mCompleteBtn.singleClick {
            //成功/重新支付
            notifyResult()
            outcomesParam.bizExt.apply {
                AutoTrace.get().upload(
                    OutcomesSceneTechTrace.payresultDoneBtn(
                        this.countryCode,
                        this.source,
                        this.partnerOrder,
                        this.processToken,
                        this.partnerCode
                    )
                )
            }

        }
        mLeftBtn.singleClick {
            //取消
            notifyResult()
            outcomesParam.bizExt.apply {
                AutoTrace.get().upload(
                    OutcomesSceneTechTrace.payresultExitBtn(
                        this.countryCode,
                        this.source,
                        this.partnerOrder,
                        this.processToken,
                        this.partnerCode
                    )
                )
            }
        }
        mRightBtn.singleClick {
            //重试
            queryResult(outcomesParam)
            outcomesParam.bizExt.apply {
                AutoTrace.get().upload(
                    OutcomesSceneTechTrace.payresultRetryBtn(
                        this.countryCode,
                        this.source,
                        this.partnerOrder,
                        this.processToken,
                        this.partnerCode
                    )
                )
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initObserve() {
        /**
         * 预下单结果查询 普通支付 签约并支付 纯签约
         */
        payResultViewModel.outcomesPrePayResponse.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    PayLogUtil.i(
                        TAG,
                        "outcomesPrePayResponse orderStatus: ${it.data?.status} -- partnerOrder: ${it.data?.partnerOrder}"
                    )
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.SUCCESS.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0009.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultSuccess(buildCommonMap)
                    outcomesPrePayResponse = it.data ?: OutcomesPrePayResponse()
                    if (TYPE_SIGN == outcomesParam.transType) {
                        switchStatueRequest(
                            outcomesPrePayResponse.goodsDetail?.goodsSignStatus ?: "",
                            outcomesPrePay = outcomesPrePayResponse
                        )
                    } else {
                        switchStatueRequest(
                            outcomesPrePayResponse.status,
                            outcomesPrePay = outcomesPrePayResponse
                        )
                    }
                    payWithResultStatic(
                        GSON.toJson(outcomesPrePayResponse),
                        GSON.toJson(outcomesParam),
                        "${outcomesPrePayResponse.creditDetail?.creditCount ?: ""}"
                    )
                }

                Status.ERROR -> {
                    TipsHelper.showErrorTips(it.code, it.message, OutcomesRouter.CODE_LIST)
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.ERROR.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0010.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultError(buildCommonMap)
                    switchStatueRequest(mResult)
                    payWithResultStatic("${it.code}\t${it.message}", GSON.toJson(outcomesParam))
                }

                Status.LOADING -> {
                }
            }
        }

        /**
         * 结果查询 普通支付
         */
        payResultViewModel.outcomesResponse.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    PayLogUtil.i(
                        TAG,
                        "outcomesResponse orderStatus: ${it.data?.orderStatus} -- signStatus: ${it.data?.payType}"
                    )
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.SUCCESS.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0009.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultSuccess(buildCommonMap)
                    DiffUpgradeHelper.loadEnd("OutcomesAcrossActivity", hashCode())
                    val outcomesResponse = it.data ?: OutcomesResponse()
                    switchStatueRequest(outcomesResponse.orderStatus, outcomes = outcomesResponse)
                    payWithResultStatic(
                        GSON.toJson(outcomesResponse),
                        GSON.toJson(outcomesParam),
                        "${outcomesResponse.creditCount}"
                    )
                }

                Status.ERROR -> {
                    DiffUpgradeHelper.loadEnd("OutcomesAcrossActivity", hashCode())
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.ERROR.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0010.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultError(buildCommonMap)
                    TipsHelper.showErrorTips(it.code, it.message, OutcomesRouter.CODE_LIST)
                    switchStatueRequest(mResult)
                    payWithResultStatic("${it.code}\t${it.message}", GSON.toJson(outcomesParam))
                }

                Status.LOADING -> {
                }
            }
        }
        /**
         * 结果查询 签约或签约并支付
         */
        payResultViewModel.outcomesSignResponse.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    PayLogUtil.i(
                        TAG,
                        "outcomesSignResponse orderStatus: ${it.data?.orderStatus} -- signStatus: ${it.data?.signStatus}"
                    )
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.SUCCESS.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0009.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultSuccess(buildCommonMap)
                    DiffUpgradeHelper.loadEnd("OutcomesAcrossActivity", hashCode())
                    val outcomesSignResponse = it.data ?: OutcomesSignResponse()
                    PayLogUtil.i(
                        TAG,
                        "orderStatus: $outcomesSignResponse.orderStatus -- signStatus: ${outcomesSignResponse.signStatus}"
                    )
                    if (TYPE_SIGN == outcomesParam.transType) {
                        //纯签约
                        switchStatueRequest(
                            outcomesSignResponse.signStatus,
                            outcomesSign = outcomesSignResponse
                        )
                    } else {
                        //支付并签约
                        switchStatueRequest(
                            outcomesSignResponse.orderStatus,
                            outcomesSign = outcomesSignResponse
                        )
                    }
                    payWithResultStatic(
                        GSON.toJson(outcomesSignResponse),
                        GSON.toJson(outcomesParam)
                    )
                }

                Status.ERROR -> {
                    DiffUpgradeHelper.loadEnd("OutcomesAcrossActivity", hashCode())
                    TipsHelper.showErrorTips(it.code, it.message, OutcomesRouter.CODE_LIST)
                    val buildCommonMap = StaticHelper.buildCommonMap(
                        processToken = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        bizNode = BizNode.TRANSACTION_RESULT.value,
                        orderType = outcomesParam.bizExt.contractType,
                        bizResult = BizResult.ERROR.value,
                        bizCode = TransactionResultStatusCodes.CODE_05_000_0010.statusCode,
                        outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                            put("channelId", outcomesParam.channelId)
                            put("payOrder", outcomesParam.payOrder)
                            put("transType", "${outcomesParam.transType}")
                            put("isAcrossType", "false")
                        })
                    )
                    StaticHelper.eventIdQueryResultError(buildCommonMap)
                    switchStatueRequest(mResult)
                    payWithResultStatic("${it.code}\t${it.message}", GSON.toJson(outcomesParam))
                }

                Status.LOADING -> {
                    // nothing
                }
            }
        }
        /**
         * 极速支付推荐信息
         */
        val fastPayObserver = FastPayObserver(payResultViewModel, outcomesParam)
        lifecycle.addObserver(fastPayObserver)
        fastPayObserver.asLiveData().observe(this) { recommend ->
            mRecommondCheck.visibility = if (TextUtils.isEmpty(recommend.text)) {
                View.GONE
            } else {
                AutoTrace.get().upload(
                    OutcomesSceneTrace.eventIdPayresultPaytypeShow(
                        token = outcomesParam.bizExt.processToken,
                        order = outcomesParam.bizExt.partnerOrder,
                        partnerId = outcomesParam.bizExt.partnerCode,
                        countryCode = outcomesParam.bizExt.countryCode,
                        currencyCode = outcomesParam.bizExt.currency ?: "",
                        amount = outcomesParam.bizExt.actualAmount,
                        source = outcomesParam.bizExt.source,
                        packageName = packageName,
                        productName = outcomesParam.bizExt.productName ?: "",
                        isLogin = if (!TextUtils.isEmpty(outcomesParam.bizExt.processToken)) "1" else "0",
                        screenType = outcomesParam.bizExt.screenType,
                        prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                        isPrePay = if (TextUtils.isEmpty(outcomesParam.bizExt.prePayToken)) {
                            "0"
                        } else {
                            "1"
                        },
                        defaultPaytype = outcomesParam.channelId,
                        DefaultPay = outcomesParam.bizExt.defaultPayType
                    )
                )

                View.VISIBLE
            }
            mRecommondCheck.text = Html.fromHtml(recommend.text)
            mRecommondCheck.isChecked = recommend.status
        }
    }

    /**
     * 数据初始化
     * 发送支付状态查询、营销位请求
     */
    private fun initData() {
        DiffUpgradeHelper.loadStart("OutcomesAcrossActivity", hashCode())
        queryResult(outcomesParam)
    }

    private fun changeButtonAreaVisibility(buttonOne: Int, buttonTwo: Int) {
        mCompleteBtn.visibility = buttonOne
        mTwoBtnLayout.visibility = buttonTwo
    }

    private fun queryResult(resultParam: OutcomesParam) {
        if (!TextUtils.isEmpty(resultParam.bizExt.prePayToken)) {
            payResultViewModel.outComesPrePayParam.value = resultParam
        } else {
            resultParam.transType?.apply {
                if (TransType.PAY.type == this || TransType.PAYMENT.type == this) {
                    payResultViewModel.outComesParam.value = resultParam
                } else {
                    payResultViewModel.outComesSingParam.value = resultParam
                }
            } ?: kotlin.run {
                payResultViewModel.outComesParam.value = resultParam
            }
        }
    }


    /**
     * 切换结果查询状态
     */
    private fun switchStatueRequest(
        status: String,
        outcomes: OutcomesResponse? = null,
        outcomesSign: OutcomesSignResponse? = null,
        outcomesPrePay: OutcomesPrePayResponse? = null
    ) {
        when (status) {
            STATUS_INIT -> {
                mResult = STATUS_INIT
                onResultInit()
            }

            STATUS_FAIL -> {
                mResult = STATUS_FAIL
                onResultFail(outcomesPrePay)
            }

            STATUS_OK -> {
                mResult = STATUS_OK
                payResultViewModel.paySuccess.value = true
                //普通支付
                if (outcomes != null) {
                    onPayResultOk(outcomes)
                }
                if (outcomesPrePay != null) {
                    if (TYPE_SIGN != outcomesParam.transType && TYPE_SIGN_AND_PAY != outcomesParam.transType) {
                        onPrePayResultOk(outcomesPrePay)
                    } else {
                        onPrePaySignResultOk(outcomesPrePay)
                    }
                }
                //签约、签约并支付
                if (outcomesSign != null) {
                    onSignResultOk(outcomesSign)
                }
            }
        }
    }

    /**
     * 处理支付结果成功的状态
     */
    private fun onPrePayResultOk(outcomes: OutcomesPrePayResponse) {
        //默认展示"支付成功"
        var payResultExpand = R.string.pay_result_expand_success
        //当notifyStatus为OK时展示"支付成功，已通知商户发货"
        outcomes.notifyStatus?.let {
            if (it == NOTIFY_STATUS_OK) {
                payResultExpand = R.string.pay_result_expand_success_notify_merchant_ship
            }
        }
        mResult = STATUS_OK
        mPayResult?.showResult(
            R.drawable.opay_ft_outcomes_success_icon,
            payResultExpand,
            PayResultArea.STATE_SUCCESS
        )
        setStateTextViewColorAndSize(R.color.opay_ft_outcomes_pay_result_state, 20f)
        changeButtonAreaVisibility(View.VISIBLE, View.GONE)
        mCompleteBtn.isEnabled = true
        if (BizHelper.isFlavorChina) {
            // 显示查看订单按钮
            mViewOrderBtn.visibility = View.VISIBLE
        }
        payResultViewModel.showFreePassTips(outcomesParam, outcomes)
        //技术埋点，成状态下，统计支付成功和支付成功已联系商户发货的量
        outcomes.apply {
            AutoTrace.get().upload(
                OutcomesSceneTechTrace.eventIdPrePayResultOk(
                    notifyStatus = this.notifyStatus,
                    statusDesc = getString(payResultExpand),
                    countryCode = outcomesParam.bizExt.countryCode,
                    source = outcomesParam.bizExt.source,
                    order = outcomesParam.bizExt.partnerOrder,
                    token = outcomesParam.bizExt.processToken,
                    partnerId = outcomesParam.bizExt.partnerCode
                )
            )
        }
    }

    /**
     * 处理支付结果成功的状态
     */
    private fun onPrePaySignResultOk(outcomes: OutcomesPrePayResponse) {
        mResult = STATUS_OK
        if (TYPE_SIGN == outcomesParam.transType) {
            //签约并支付 虚拟资产抵扣后交易类型是"SIGN"，用商品价格区分
            if (outcomes.goodsDetail?.goodsPrice == 0) {
                mViewOrderBtn.visibility = View.GONE
            } else {
                if (BizHelper.isFlavorChina) {
                    mViewOrderBtn.visibility = View.VISIBLE
                }
            }
            mPayResult?.showResult(
                R.drawable.opay_ft_outcomes_success_icon,
                R.string.pay_result_open_success,
                PayResultArea.STATE_SUCCESS
            )
        } else {
            if (BizHelper.isFlavorChina) {
                // 显示查看订单按钮
                mViewOrderBtn.visibility = View.VISIBLE
            }
            mPayResult?.showResult(
                R.drawable.opay_ft_outcomes_success_icon,
                R.string.pay_result_expand_success,
                PayResultArea.STATE_SUCCESS
            )
        }
        setStateTextViewColorAndSize(R.color.opay_ft_outcomes_pay_result_state, 20f)
        changeButtonAreaVisibility(View.VISIBLE, View.GONE)
        mCompleteBtn.isEnabled = true
        if (!outcomes.freePwdDetail?.channelUserLoginId.isNullOrEmpty()) {
            if (BizHelper.isFlavorChina) {
                return
            }
            ToastUtil.show(
                AppRuntime.getAppContext().getString(
                    R.string.pay_result_open_free_pass_sccuess_msg,
                    outcomesParam.bizExt.channelName,
                    outcomes.freePwdDetail?.channelUserLoginId
                )
            )
        }

        if (!outcomes.channelUserLoginId.isNullOrEmpty()) {
            if (BizHelper.isFlavorChina) {
                return
            }
            ToastUtil.show(
                AppRuntime.getAppContext().getString(
                    R.string.pay_result_open_free_pass_sccuess_msg,
                    outcomesParam.bizExt.channelName,
                    outcomes.channelUserLoginId
                )
            )
        }
    }

    /**
     * 处理支付结果成功的状态
     */
    private fun onPayResultOk(outcomes: OutcomesResponse) {
        mResult = STATUS_OK
        mPayResult.showResult(
            R.drawable.opay_ft_outcomes_success_icon,
            R.string.pay_result_expand_success,
            PayResultArea.STATE_SUCCESS
        )
        setStateTextViewColorAndSize(R.color.opay_ft_outcomes_pay_result_state, 20f)
        changeButtonAreaVisibility(View.VISIBLE, View.GONE)
        mCompleteBtn.isEnabled = true
        if (BizHelper.isFlavorChina) {
            // 显示查看订单按钮
            mViewOrderBtn.visibility = View.VISIBLE
        }
        // 加购商品描述
        if (!TextUtils.isEmpty(outcomes.attachGoodsName)) {
            outcomesParam.apply {
                AutoTrace.get().upload(
                    OutcomesSceneTrace.buyPlaceSuccess(
                        this.payOrder,
                        this.bizExt.countryCode,
                        this.bizExt.source,
                        outcomes.attachGoodSignStatus ?: "",
                        this.bizExt.partnerOrder,
                        this.bizExt.processToken,
                        this.bizExt.partnerCode
                    )
                )
            }
        }
        payResultViewModel.showFreePassTips(outcomesParam, outcomes)

    }

    /**
     * 处理支付结果成功的状态
     */
    private fun onSignResultOk(outcomesSign: OutcomesSignResponse) {
        mResult = STATUS_OK
        if (TYPE_SIGN == outcomesParam.transType) {
            // 纯签约隐藏查看订单按钮
            mViewOrderBtn.visibility = View.GONE
            mPayResult.showResult(
                R.drawable.opay_ft_outcomes_success_icon,
                R.string.pay_result_open_success,
                PayResultArea.STATE_SUCCESS
            )
        } else {
            if (BizHelper.isFlavorChina) {
                // 显示查看订单按钮
                mViewOrderBtn.visibility = View.VISIBLE
            }
            mPayResult.showResult(
                R.drawable.opay_ft_outcomes_success_icon,
                R.string.pay_result_expand_success,
                PayResultArea.STATE_SUCCESS
            )
        }
        setStateTextViewColorAndSize(R.color.opay_ft_outcomes_pay_result_state, 20f)
        changeButtonAreaVisibility(View.VISIBLE, View.GONE)
        mCompleteBtn.isEnabled = true
        if (!outcomesSign.channelUserLoginId.isNullOrEmpty()) {
            if (BizHelper.isFlavorChina) {
                return
            }
            ToastUtil.show(
                AppRuntime.getAppContext().getString(
                    R.string.pay_result_open_free_pass_sccuess_msg,
                    outcomesParam.bizExt.channelName,
                    outcomesSign.channelUserLoginId
                )
            )
        }
    }

    /**
     * 处理支付结果失败的状态
     */
    private fun onResultFail(outcomes: OutcomesPrePayResponse?) {
        val showTipStrId =
            if (TYPE_SIGN == outcomesParam.transType) R.string.pay_result_only_sign_fail else R.string.pay_result_expand_fail
        if (TYPE_SIGN == outcomesParam.transType) {
            outcomes?.let {
                //签约并支付 虚拟资产抵扣后交易类型是"SIGN"，用商品价格区分
                if (it.goodsDetail?.goodsPrice == 0) {
                    mViewOrderBtn.visibility = View.GONE
                } else {
                    if (BizHelper.isFlavorChina) {
                        mViewOrderBtn.visibility = View.VISIBLE
                    }
                }
            }
        } else {
            if (BizHelper.isFlavorChina) {
                mViewOrderBtn.visibility = View.VISIBLE
            }
        }
        mResult = STATUS_FAIL
        mPayResult.showResult(
            com.oplus.pay.ui.R.drawable.opay_lib_ui_fail_icon,
            showTipStrId,
            PayResultArea.STATE_FAIL
        )
        setStateTextViewColorAndSize(R.color.opay_ft_outcomes_pay_result_state, 20f)
        changeButtonAreaVisibility(View.VISIBLE, View.GONE)
        mCompleteBtn.isEnabled = true
    }

    private fun setStateTextViewColorAndSize(color: Int, size: Float) {
        //设置字体大小
        mPayResult.setTextStateViewTextSize(size)
    }

    /**
     * 处理支付结果未知的状态
     */
    private fun onResultInit() {
        val realReceiveId =
            if (TYPE_SIGN == outcomesParam.transType) R.string.pay_result_auto_renew_sub_ongoning_msg else R.string.receive_result_need
        mPayResult.showResult(
            R.drawable.opay_ft_outcomes_noresult_icon,
            realReceiveId,
            PayResultArea.STATE_NO_RESULT
        )
        //设置字体大小和颜色
        setStateTextViewColorAndSize(com.oplus.pay.ui.R.color.opay_lib_ui_color_black_alpha_45, 12f)
        // 隐藏查看订单按钮
        mViewOrderBtn.visibility = View.GONE

        // 如果此时距离进入结果页面不满一秒钟，则不显示"重试/退出"按钮。
        val timeDiff = System.currentTimeMillis() - payResultViewModel.enterTime
        changeButtonAreaVisibility(View.GONE, if (timeDiff < 1000) View.GONE else View.VISIBLE)

        payResultViewModel.polling {
            queryResult(outcomesParam)
        }
    }

    /**
     * 跳转到订单详情页面
     */
    private fun navigateToBillDetail() {
        com.oplus.pay.subscription.PaySubscriptionHelper.launchBillDetail(
            context = this,
            processToken = outcomesParam.bizExt.processToken,
            bizExt = outcomesParam.bizExt,
            payOrder = outcomesParam.payOrder,
            paymentCode = outcomesPrePayResponse.paymentCode,
            paymentName = outcomesPrePayResponse.paymentName,
            appName = outcomesPrePayResponse.appName,
            payAmount = outcomesPrePayResponse.payAmount.toString(),
            originalAmount = outcomesPrePayResponse.originalAmount.toString(),
            productName = outcomesParam.bizExt.productName,
            paymentResult = when (mResult) {
                STATUS_OK -> "SUCCESS"
                STATUS_FAIL -> "FAILURE"
                else -> "FAILURE"
            },
            transactionStatusDesc = when (mResult) {
                STATUS_OK -> getString(R.string.pay_result_expand_success)
                STATUS_FAIL -> getString(R.string.pay_result_expand_fail)
                else -> getString(R.string.pay_result_expand_fail)
            },
            detailIcon = outcomesPrePayResponse.detailIcon,
            transactionDateTime = if (STATUS_OK == mResult) outcomesPrePayResponse.successTime else outcomesPrePayResponse.requestTime,
            orderAmount = outcomesPrePayResponse.originalAmount.toString(),
            promotionDiscountAmount = if (STATUS_OK == mResult) outcomesPrePayResponse.promotionDiscountAmount.toString() else "0",
            keBiDeductAmount = if (STATUS_OK == mResult) {
                if (outcomesPrePayResponse.cocoinDetail != null) outcomesPrePayResponse.cocoinDetail?.cocoinDeducAmount.toString() else "0"
            } else {
                "0"
            },
            voucherDeductAmount = if (STATUS_OK == mResult) outcomesPrePayResponse.voucherAmount.toString() else "0",
            creditDeductAmount = if (STATUS_OK == mResult) {
                if (outcomesPrePayResponse.creditDetail != null) outcomesPrePayResponse.creditDetail?.creditDeductAmount.toString() else "0"
            } else {
                "0"
            }
        )
    }

    override fun onClick(v: View) {
        when (v.id) {
            // 重试
            R.id.btn_bottom_right -> {
                queryResult(outcomesParam)
                outcomesParam.bizExt.apply {
                    AutoTrace.get()
                        .upload(
                            OutcomesSceneTechTrace.payresultRetryBtn(
                                this.countryCode,
                                this.source,
                                this.partnerOrder,
                                this.processToken,
                                this.partnerCode
                            )
                        )
                }
            }
        }

    }

    private fun notifyResult() {
        when (mResult) {
            STATUS_INIT -> {
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = outcomesParam.bizExt.processToken,
                    order = outcomesParam.bizExt.partnerOrder,
                    prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                    bizNode = BizNode.TRANSACTION_RESULT.value,
                    orderType = outcomesParam.bizExt.contractType,
                    bizResult = BizResult.ERROR.value,
                    bizCode = TransactionResultStatusCodes.CODE_05_000_0002.statusCode,
                    outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                        put("channelId", outcomesParam.channelId)
                        put("payOrder", outcomesParam.payOrder)
                        put("transType", "${outcomesParam.transType}")
                        put("isAcrossType", "true")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdTransactionResult(buildCommonMap)
                OutcomesHelper.notifyUnknown(outcomesParam)
            }

            STATUS_FAIL -> {
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = outcomesParam.bizExt.processToken,
                    order = outcomesParam.bizExt.partnerOrder,
                    prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                    bizNode = BizNode.TRANSACTION_RESULT.value,
                    orderType = outcomesParam.bizExt.contractType,
                    bizResult = BizResult.ERROR.value,
                    bizCode = TransactionResultStatusCodes.CODE_05_000_0001.statusCode,
                    outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                        put("channelId", outcomesParam.channelId)
                        put("payOrder", outcomesParam.payOrder)
                        put("transType", "${outcomesParam.transType}")
                        put("isAcrossType", "true")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdTransactionResult(buildCommonMap)
                // 重新支付
                OutcomesHelper.notifyUnknown(outcomesParam)
            }

            STATUS_OK -> {
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = outcomesParam.bizExt.processToken,
                    order = outcomesParam.bizExt.partnerOrder,
                    prePayToken = outcomesParam.bizExt.prePayToken ?: "",
                    bizNode = BizNode.TRANSACTION_RESULT.value,
                    orderType = outcomesParam.bizExt.contractType,
                    bizResult = BizResult.SUCCESS.value,
                    bizCode = TransactionResultStatusCodes.CODE_05_000_0000.statusCode,
                    outputResult = GSON.toJson(mutableMapOf<String, String>().apply {
                        put("channelId", outcomesParam.channelId)
                        put("payOrder", outcomesParam.payOrder)
                        put("transType", "${outcomesParam.transType}")
                        put("isAcrossType", "true")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdTransactionResult(buildCommonMap)
                // 通知业务方支付结果
                OutcomesHelper.notifySuccess(outcomesParam)
            }
        }

        finish()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            logBackAction("PHYSICAL_BACK_KEY")
            true
        } else super.onKeyDown(keyCode, event)
    }

    override fun onBackPressed() {
        // 拦截系统返回键（包括导航栏返回键）
        logBackAction("SYSTEM_BACK_PRESSED")
        // 不调用super.onBackPressed()来阻止默认返回行为
    }

    /**
     * 记录返回操作埋点
     */
    private fun logBackAction(actionType: String) {
        // 使用OutcomesSceneTrace.eventIdPayResultBack进行埋点上报
        outcomesParam.bizExt.apply {
            AutoTrace.get().upload(
                OutcomesSceneTrace.eventIdPayResultBack(
                    token = this.processToken,
                    order = this.partnerOrder,
                    appVersion = BizHelper.getAppVersion(),
                    screenType = this.screenType,
                    keyType = actionType
                )
            )
        }
    }

    private fun setScrollChangeListener(scroll_view: NestedScrollView, divider_line_btn: View) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            scroll_view.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
                val isTop = scrollY == 0
                val isDown =
                    v!!.scrollY + v.height - v.paddingTop - v.paddingBottom == scroll_view.getChildAt(
                        0
                    ).height
                if (scrollY != oldScrollY && !isTop && !isDown) {
                    //显示分割线
                    divider_line_btn.visibility = View.VISIBLE
                } else {
                    // 不显示分割线
                    divider_line_btn.visibility = View.GONE
                }
            }
        }
    }



    /**
     * 带支付结果的埋点上报
     * @param success 是否查询成功
     * @param creditNum 抵扣积分
     * @param result 支付结果
     */
    private fun payWithResultStatic(result: String, request: String, creditNum: String = "") {
        outcomesParam.apply {
            //支付结果页面自带结果曝光
            AutoTrace.get().upload(
                OutcomesSceneTrace.payresultShow(
                    payResult = result,
                    request = request,
                    payType = this.channelId,
                    countryCode = this.bizExt.countryCode,
                    source = this.bizExt.source,
                    order = this.bizExt.partnerOrder,
                    token = this.bizExt.processToken,
                    partnerId = this.bizExt.partnerCode,
                    amount = "${bizExt.actualAmount}",
                    packageName = appPackage,
                    productName = bizExt.productName ?: "",
                    screenType = bizExt.screenType
                )
            )
            AutoTrace.get().upload(
                OutcomesSceneTrace.payresultRequest(
                    result,
                    request,
                    outcomesParam.channelId,
                    this.bizExt.screenType,
                    creditNum,
                    this.bizExt.countryCode,
                    this.bizExt.source,
                    this.bizExt.partnerOrder,
                    this.bizExt.processToken,
                    this.bizExt.partnerCode
                )
            )
            this.bizExt.apply {
                AutoTrace.get().upload(
                    OutcomesSceneTechTrace.payresultShowTech(
                        outcomesParam.transType ?: "",
                        result,
                        request,
                        creditNum,
                        screenType,
                        outcomesParam.channelId,
                        this.countryCode,
                        this.source,
                        this.partnerOrder,
                        this.processToken,
                        this.partnerCode
                    )
                )
            }


        }
    }
}