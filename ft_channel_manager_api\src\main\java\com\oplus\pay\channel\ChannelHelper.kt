package com.oplus.pay.channel

import android.app.Activity
import androidx.activity.ComponentActivity
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import com.alibaba.android.arouter.launcher.ARouter
import com.oplus.pay.basic.Resource
import com.oplus.pay.basic.util.livedata.AbsentLiveData
import com.oplus.pay.biz.BizHelper
import com.oplus.pay.biz.OrderType
import com.oplus.pay.channel.api.ChannelRouter
import com.oplus.pay.channel.install.IChannelAppInstallHandler
import com.oplus.pay.channel.model.AutoDebitInfo
import com.oplus.pay.channel.model.request.ChannelCheckParam
import com.oplus.pay.channel.model.request.FastPayRecommendationParam
import com.oplus.pay.channel.model.request.FastPayTypeParam
import com.oplus.pay.channel.model.request.OpenChannelParams
import com.oplus.pay.channel.model.request.V2PayTypesParam
import com.oplus.pay.channel.model.response.FastPayRecommendationResponse
import com.oplus.pay.channel.model.response.FastPayTypeResponse
import com.oplus.pay.channel.model.response.V2Channel
import com.oplus.pay.channel.model.response.V2PayTypes
import com.oplus.pay.channel.polling.IChannelPollPayResult
import com.oplus.pay.channel.polling.IChannelPollingTaskObserver
import com.oplus.pay.channel.polling.IChannelQueryPayResult
import com.oplus.pay.channel.provider.IChannelAppInstallProvider
import com.oplus.pay.channel.provider.IChannelManagerProvider
import com.oplus.pay.channel.provider.IChannelPollProvider
import com.oplus.pay.channel.provider.IChannelProvider
import com.oplus.pay.channel.provider.IQRViewProvider
import com.oplus.pay.order.model.request.OrderInfo
import java.lang.ref.SoftReference

/**
 * <p>Title: ChannelProvider</p>
 * <p>Description: ChannelProvider</p>
 * <p>Copyright (c) 2021 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR> by 80230486 on 2021/4/17.
 *
 */
object ChannelHelper {

    private fun getChannelManagerProvider(): IChannelManagerProvider? {
        val navigation = try {
            ARouter.getInstance().build(ChannelRouter.PATH_PROVIDER).navigation()
        } catch (e: Exception) {
            null
        }
        return if (navigation != null) {
            navigation as IChannelManagerProvider
        } else {
            null
        }
    }

    fun getPayTypes(payTypesParam: V2PayTypesParam): LiveData<Resource<V2PayTypes>> {
        return getChannelManagerProvider()?.getPayTypes(payTypesParam)
            ?: AbsentLiveData.create()
    }

    /**
     * 根据渠道ID获取渠道信息
     * @param paymentCode 支付方式编码
     * @param channelCode 支付渠道编码
     * @param channelPaymentScene 支付场景，用于区分app、h5、小程序等
     * @param paymentType 支付类型，用于区分普通支付、快捷支付和周期扣款等
     *
     */
    fun getChannel(
        paymentCode: String = "",
        paymentType: String = "",
        channelCode: String = ""
    ): IChannelProvider? {
        return getChannel(
            paymentCode = paymentCode,
            paymentType = paymentType,
            channelCode = channelCode,
            channelPaymentScene = ""
        )
    }

    /**
     * 根据渠道ID获取渠道信息
     * @param paymentCode 支付方式编码
     * @param channelCode 支付渠道编码
     * @param channelPaymentScene 支付场景，用于区分app、h5、小程序等
     * @param paymentType 支付类型，用于区分普通支付、快捷支付和周期扣款等
     */
    fun getChannel(
        paymentCode: String,
        paymentType: String,
        channelCode: String = "",
        channelPaymentScene: String = ""
    ): IChannelProvider? {
        return getChannelManagerProvider()?.getChannel(paymentCode, paymentType, channelCode, channelPaymentScene)
    }

    /**
     * 非支付中纯签约，用于拉起渠道消费预签约信息完成签约
     * @param activity Activity 调用渠道纯签约的容器
     * @param channelId 根据渠道Id获取渠道信息 内销使用paytype 外销使用channel字段进行路由
     * @param autoDebitInfo 调用渠道存签约接口后渠道返回的预签约信息，拉起渠道完成消费（如sdk/H5）即完成签约
     * @param processToken 用于标识业务场景唯一性
     */
    fun autoDebit(
        activity: Activity,
        channelId: String,
        paymentType: String = "",
        autoDebitInfo: String,
        processToken: String
    ): LiveData<Resource<String?>> {
        val channel = if (BizHelper.isFlavorChina) getChannel(
            paymentCode = channelId,
            paymentType = paymentType
        ) else getChannel(channelCode = channelId, paymentType = paymentType)
        return channel
            ?.autoDebit(
                activity = activity,
                autoDebitInfo = autoDebitInfo,
                processToken = processToken
            ) ?: AbsentLiveData.create()
    }

    /**
     * 渠道快捷支付，调用交易中心开放的快捷支付能力.收银台调用该能力即可
     * @param activity 上下文
     * @param orderInfo 收银台透传到渠道的订单信息
     * @param orderType 订单类型，标识是否是支付/签约/充值 外销不需要
     */
    fun channelQuickPay(
        activity: Activity,
        channelId: String,
        orderInfo: OrderInfo,
        orderType: OrderType? = null
    ): LiveData<Resource<String>> {
        return getChannel(channelId)
            ?.channelQuickPay(
                activity = activity,
                orderInfo = orderInfo,
                orderType = orderType
            ) ?: AbsentLiveData.create()
    }


    /**
     * 非支付中纯签约需要补全信息后再调用非支付中签约接口获取预签约信息
     * @param activity Activity 调用渠道纯签约的容器
     * @param channelId 根据渠道Id获取渠道信息 内销使用paytype 外销使用channel字段进行路由
     * @param autoDebitInfo 非支付中签约信息
     */
    fun replenishAutoDebit(
        activity: Activity,
        channelId: String,
        autoDebitInfo: AutoDebitInfo
    ): LiveData<Resource<String?>> {
        //该场景外销需要通过paymentCode 获取渠道路由信息channelCode
        return getChannel(channelId)
            ?.replenishAutoDebit(
                activity = activity,
                autoDebitInfo = autoDebitInfo
            ) ?: AbsentLiveData.create()
    }


    /**
     * 打开支付渠道，进行支付
     */
    fun pay(activity: Activity, openChannelParams: OpenChannelParams) {
        getChannelManagerProvider()?.pay(activity, openChannelParams)
    }

    /**
     * 打开支付渠道，补全信息并且支付
     */
    fun replenishAndPay(activity: Activity, orderInfo: OrderInfo): LiveData<Resource<String>> {
        return getChannelManagerProvider()?.replenishAndPay(activity, orderInfo)
            ?: AbsentLiveData.create()
    }

    /**
     * 检查渠道部分登录 是否存在档位匹配，匹配不进行oppo帐号登录以及档位选择
     */
    fun checkMatch(payActualAmount: String?, selectAmounts: List<String>?): Boolean {
        return getChannelManagerProvider()?.checkMatch(payActualAmount, selectAmounts) ?: false

    }

    /**
     * 查询默认支付方式是否支持
     */
    fun getFastPayType(param: FastPayTypeParam): LiveData<Resource<FastPayTypeResponse>> {
        return getChannelManagerProvider()?.getFastPayType(param) ?: AbsentLiveData.create()
    }

    /**
     * 查询默认支付方式推荐信息
     */
    fun getFastPayRecommendation(param: FastPayRecommendationParam): LiveData<Resource<FastPayRecommendationResponse>> {
        return getChannelManagerProvider()?.getFastPayRecommendation(param)
            ?: AbsentLiveData.create()
    }

    /**
     * 校验渠道参数
     * @param param 渠道相关参数
     * @return 返回校验结果 成功或失败
     */
    fun checkParams(param: ChannelCheckParam): LiveData<Resource<String>> {
        return getChannelManagerProvider()?.checkParams(param)
            ?: AbsentLiveData.create()
    }


    /**
     * 初始化 渠道反查实例 上层业务自行维护
     * @param activityRef 渠道反查所在的容器
     * @param payType 当前的支付方式
     * @param token 用户埋点的processToken
     * @param pollingDialogCancelAble 反查loading对话框是否可取消，默认可取消
     *
     */
    fun initChannelPollProvider(
        activityRef: SoftReference<FragmentActivity>, payType: String, token: String,
        pollingDialogCancelAble: Boolean = true,
        isNeedConfirmDialog: Boolean = true,
    ): IChannelPollPayResult? {
        return getChannelPollProvider()?.initChannelPollObserver(
            activityRef = activityRef,
            payType = payType,
            token = token,
            pollingDialogCancelAble = pollingDialogCancelAble,
            isNeedConfirmDialog = isNeedConfirmDialog,
        )
    }


    /**
     * 渠道“静默”支付结果反查
     * @param activityRef 渠道反查所在的容器
     * @param payType 当前的支付方式
     * @param token 用户埋点的processToken
     * @param task 反查方法的具体实现，上层业务自行处理
     */
    fun initChannelPollingTask(
        activityRef: SoftReference<FragmentActivity>,
        payType: String,
        token: String,
        task: Runnable
    ): IChannelPollingTaskObserver? {
        return getChannelPollProvider()?.initChannelPollingTask(
            activityRef = activityRef,
            payType = payType,
            token = token,
            task = task
        )
    }

    fun initChannelQueryPayObserver(
        activityRef: SoftReference<FragmentActivity>,
        payType: String
    ): IChannelQueryPayResult? {
        return getChannelPollProvider()?.initChannelQueryPayObserver(
            activityRef = activityRef,
            payType = payType
        )
    }


    /**
     * 初始化应用安装处理器，上层业务自行维护实例对象，避免单例导致的状态覆盖
     * @param activityRef 当前交互UI依赖的组件
     * @param pkgName 应用对应的包名
     * @param downloadTitleStr 是否下载应用对话框 title提示
     * @param installingTipStr 下载安装过程中对话框title
     * @param isAcrossScreen 是否横屏 ，横屏对话框居中显示
     * @param payType 支付方式 用于埋点上报
     * @param processToken 用于埋点上报
     * @return IChannelAppInstallHandler 应用下载安装逻辑处理接口，业务上层维护该实例，并调用该实例实现应用下载安装
     */
    fun initAppInstallHandler(
        activityRef: SoftReference<ComponentActivity>,
        pkgName: String,
        downloadTitleStr: String,
        downloadMsgStr: String,
        installingTipStr: String,
        isAcrossScreen: Boolean = false,
        payType: String,
        processToken: String
    ): IChannelAppInstallHandler? {
        return getChannelAppInstallProvider()?.initAppInstallHandler(
            activityRef = activityRef,
            pkgName = pkgName,
            downloadTitleStr = downloadTitleStr,
            downloadMsgStr = downloadMsgStr,
            installingTipStr = installingTipStr,
            isAcrossScreen = isAcrossScreen,
            payType = payType,
            processToken = processToken
        )
    }

    /**
     * 支付渠道限额检查
     * @param amount 实际支付金额
     * @param channel 支付渠道
     * @return 返回是否触发单笔/当日限额
     */
    fun checkChannelPayAmountLimit(limitMap: MutableMap<String, String>, channel: V2Channel): Boolean =
        getChannelManagerProvider()?.checkChannelPayAmountLimit(limitMap, channel) ?: true

    /**
     * 初始化 二维码相关的view，每个容器一个实例
     */
    fun initQRViewProvider(activityRef: SoftReference<FragmentActivity>) = getQRViewProvider()?.initIQRView(activityRef)

    /**
     * 预先初始化ChannelManagerProvider arouter耗时
     */
    fun preInitChannelManagerProvider() {
        getChannelManagerProvider()
    }

    private fun getChannelPollProvider(): IChannelPollProvider? {
        val navigation = try {
            ARouter.getInstance().build(ChannelRouter.CHANNEL_POLL_PROVIDER).navigation()
        } catch (e: Exception) {
            null
        }
        return if (navigation != null) {
            navigation as IChannelPollProvider
        } else {
            null
        }
    }

    private fun getChannelAppInstallProvider(): IChannelAppInstallProvider? {
        val navigation = try {
            ARouter.getInstance().build(ChannelRouter.CHANNEL_APP_INSTALL_PROVIDER).navigation()
        } catch (e: Exception) {
            null
        }
        return if (navigation != null) {
            navigation as IChannelAppInstallProvider
        } else {
            null
        }
    }

    private fun getQRViewProvider(): IQRViewProvider? {
        val navigation = try {
            ARouter.getInstance().build(ChannelRouter.CHANNEL_QR_VIEW_PROVIDER).navigation()
        } catch (e: Exception) {
            null
        }
        return if (navigation != null) {
            navigation as IQRViewProvider
        } else {
            null
        }
    }


}