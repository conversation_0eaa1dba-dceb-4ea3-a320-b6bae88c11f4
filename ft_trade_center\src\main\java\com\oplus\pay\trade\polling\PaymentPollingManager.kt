package com.oplus.pay.trade.polling

import androidx.fragment.app.FragmentActivity
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.channel.ChannelHelper
import com.oplus.pay.channel.model.response.V2Channel
import com.oplus.pay.channel.polling.IChannelQueryPayResult
import com.oplus.pay.outcomes.model.OutcomesPrePayResponse
import com.oplus.pay.trade.model.PayRequest
import com.platform.usercenter.trace.rumtime.AutoTrace
import com.usercenter.custom.trace.TradeCenterSceneTrace
import java.lang.ref.SoftReference

/**
 * 支付反查管理器
 * 负责执行具体的反查网络请求和Loading管理
 *
 * 功能：
 * 1. 管理Loading对话框的显示和隐藏
 * 2. 执行支付结果反查网络请求
 * 3. 处理反查结果，成功时跳转到支付结果页
 * 4. 处理反查失败，隐藏Loading对话框
 */
class PaymentPollingManager(
    private val activityRef: SoftReference<FragmentActivity>,
) {

    private val TAG = "PaymentPollingManager"

    /**
     * 反查提供者
     */
    private var channelQueryProvider: IChannelQueryPayResult? = null

    /**
     * 反查结果回调
     */
    interface PollingCallback {
        fun onPollingSuccess(result: OutcomesPrePayResponse?)
        fun onPollingFailed(errorCode: String?, errorMsg: String?)
    }

    /**
     * 开始反查
     * @param orderId 订单号
     * @param channel 支付渠道
     * @param processToken 流程token
     * @param callback 反查结果回调
     */
    fun startPolling(
        payResult: PayRequest?,
        orderId: String,
        contractCode: String,
        channel: V2Channel,
        processToken: String,
        callback: PollingCallback
    ) {
        PayLogUtil.i(TAG, "startPolling: orderId=$orderId, channel=${channel.paymentCode}")

        val activity = activityRef.get()
        if (activity == null) {
            PayLogUtil.e(TAG, "startPolling: activity is null")
            callback.onPollingFailed("ACTIVITY_NULL", "Activity is null")
            return
        }

        // 方式1：使用IChannelQueryPayResult接口（如果支持的话）
        // Loading对话框由QueryChannelResultObserver负责显示
        tryChannelQueryPolling(payResult, orderId, channel, processToken, contractCode, callback)

        // 方式2：如果方式1不支持，则使用直接的Outcomes查询
        // tryDirectOutcomesQuery(orderId, channel, processToken, callback)
    }

    /**
     * 尝试使用IChannelQueryPayResult接口进行反查
     */
    private fun tryChannelQueryPolling(
        payResult: PayRequest?,
        orderId: String,
        channel: V2Channel,
        processToken: String,
        contractCode: String,
        callback: PollingCallback
    ) {
        try {
            // 初始化反查提供者
            channelQueryProvider = ChannelHelper.initChannelQueryPayObserver(
                activityRef = activityRef,
                payType = channel.paymentCode ?: ""
            )
            tradeCenterQueryResult(
                payResult?.mCountryCode.toString(),
                payResult?.prePayToken.toString(),
                contractCode, orderId, processToken, channel.paymentCode
            )
            // 开始反查
            channelQueryProvider?.startQueryChannelResult(
                pollingEndAndNotifyPaySuccessResultFunc = {
                    PayLogUtil.i(TAG, "channelQueryPolling: success callback")
                    // 这里需要获取实际的反查结果，暂时传null
                    callback.onPollingSuccess(null)
                },
                pollingEndAndNotifyPayErrorResultFunc = {
                    PayLogUtil.w(TAG, "channelQueryPolling: failed callback")
                    callback.onPollingFailed("CHANNEL_QUERY_FAILED", "Channel query failed")
                },
                countryCode = payResult?.mCountryCode,
                prePayToken = payResult?.prePayToken,
                payReqId = orderId,
                contractCode = contractCode
            )
        } catch (e: Exception) {
            PayLogUtil.e(TAG, "tryChannelQueryPolling: exception" + e.message)
            callback.onPollingFailed("EXCEPTION", e.message)
        }
    }

    fun tradeCenterQueryResult(
        countryCode: String?,
        prePayToken: String?,
        contractCode: String?,
        payReqId: String?,
        token: String?,
        paymentCode: String?
    ) {
        AutoTrace.get().upload(
            TradeCenterSceneTrace.tradeCenterQueryResult(
                countryCode = countryCode ?: "",
                prePayToken = prePayToken ?: "",
                contractCode = contractCode ?: "",
                payReqId = payReqId ?: "",
                token = token ?: "",
                paymentCode = paymentCode ?: ""
            )
        )
    }
}
