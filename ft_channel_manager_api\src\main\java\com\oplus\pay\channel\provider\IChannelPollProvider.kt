package com.oplus.pay.channel.provider

import android.content.Context
import androidx.fragment.app.FragmentActivity
import com.alibaba.android.arouter.facade.template.IProvider
import com.oplus.pay.channel.model.request.OpenChannelParams
import com.oplus.pay.channel.polling.IChannelPollPayResult
import com.oplus.pay.channel.polling.IChannelPollingTaskObserver
import com.oplus.pay.channel.polling.IChannelQueryPayResult
import java.lang.ref.SoftReference

/**
 * <p>Title: IChannelPollProvider</p>
 * <p>Description: 渠道反查</p>
 * <p>Copyright (c) 2023-2023/9/15 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR>
 * @version 1.0
 *
 */
interface IChannelPollProvider : IProvider {

    override fun init(context: Context?) {
        // nothing
    }

    /**
     * 初始化 渠道反查实例 上层业务自行维护
     * @param activityRef 渠道反查所在的容器
     * @param payType 当前的支付方式
     * @param token 用户埋点的processToken
     * @param pollingDialogCancelAble 反查loading对话框是否可取消，默认可取消
     *
     */
    fun initChannelPollObserver(
        activityRef: SoftReference<FragmentActivity>, payType: String, token: String,
        pollingDialogCancelAble: Boolean = true,
        isNeedConfirmDialog: Boolean = true
    ): IChannelPollPayResult

    fun initChannelQueryPayObserver(
        activityRef: SoftReference<FragmentActivity>,
        payType: String
    ): IChannelQueryPayResult


    fun initChannelPollingTask(
        activityRef: SoftReference<FragmentActivity>,
        payType: String,
        token: String,
        task: Runnable
    ): IChannelPollingTaskObserver
}