package com.oplus.pay.trade.viewmodel

import android.app.Activity
import android.content.res.Configuration
import android.text.TextUtils
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import com.oplus.pay.basic.AppRuntime
import com.oplus.pay.basic.Resource
import com.oplus.pay.basic.Status
import com.oplus.pay.basic.util.device.DeviceInfoHelper
import com.oplus.pay.biz.BizResultType
import com.oplus.pay.biz.ScreenType
import com.oplus.pay.channel.NO_PROMOTION_PAYMENTCODE
import com.oplus.pay.channel.PAYMENT_TYPE_FREEPWD_PAY
import com.oplus.pay.channel.model.response.V2Channel
import com.oplus.pay.marketing.model.response.*
import com.oplus.pay.order.model.response.CalculateResponse
import com.oplus.pay.order.model.response.CalculateResponseV2
import com.oplus.pay.marketing.model.response.BuyPlaceDisplayType
import com.oplus.pay.marketing.model.response.CombineOrderInfo
import com.oplus.pay.subscription.model.response.V2Assets
import com.oplus.pay.subscription.model.response.VirtualAssetDeductionStatus
import com.oplus.pay.subscription.model.response.Voucher
import com.oplus.pay.trade.model.PayRequest
import com.oplus.pay.trade.ui.across.AcrossTradeCenterFragment
import com.oplus.pay.trade.ui.buyplace.buyPlaceOne
import com.oplus.pay.trade.ui.buyplace.buyPlaceThree
import com.oplus.pay.trade.ui.buyplace.buyPlaceTwo
import com.oplus.pay.trade.usecase.CalculateUseCase
import com.oplus.pay.trade.utils.PayHelper
import com.oplus.pay.trade.utils.VoucherTypeString
import com.oplus.pay.trade.utils.converToData
import java.math.BigDecimal

class ShareStatusViewModel : ViewModel() {
    //发送本地广播告诉其他子级 孙子级fragment旋转了
    val screenRotation = MutableLiveData<Int?>(null)
    /**
     * 获取加购位
     */
    val combineOrderInfo = MutableLiveData<CombineOrderInfo?>()

    val payRequest: LiveData<PayRequest> get() = _payRequest
    private val _payRequest = MutableLiveData<PayRequest>()
    fun setPayRequest(payReq: PayRequest) {
        _payRequest.value = payReq
    }

    /**
     * 资产共享
     */
    val assets = MutableLiveData<Resource<V2Assets>>()

    /**
     * 是否选券返回
     */
    val isSelectVoucherBack = MutableLiveData(false)

    /**
     * 底部支付按钮是否可用
     */
    val isBtnBottomEnabled = MutableLiveData(true)

    /**
     * 当前支付渠道
     */
    private val _currentChannel = MutableLiveData<V2Channel>()
    val currentChannel: LiveData<V2Channel> = _currentChannel//当前选择的渠道

    /**
     * 设置当前渠道
     */
    fun onChannelSelected(channel: V2Channel?) {
        channel?.let {
            if (it.disable) {
                return
            }
            _currentChannel.value?.isChecked = false
            it.isChecked = true
            _currentChannel.value = it
        }
    }

    /**
     * 购买位checkbox状态选择
     */
    val attachCheckBox = MutableLiveData(false)

//    /**
//     * 是否需要重新加载渠道列表
//     */
//    val isReloadPayTypes = MutableLiveData(false)
    /**
     * 权益-架构位 勾选/取消勾选 事件处理（刷新券）
     */
    val buyPlaceToTradeByListener: MutableLiveData<Boolean> = MutableLiveData()

    /**
     * 会员优惠卷对象
     */
    var reciveVoucher = MutableLiveData<Voucher?>()

    //渠道列表是否为空
    val empty = MutableLiveData(false)

    //网络出错
    val error = MutableLiveData(false)


    /**
     * 六个核心数据指标用来计算的
     */
    val isEnough = MutableLiveData<Boolean>()  //可币余额+ 可币券是否足够支付
    val kebiAmount = MutableLiveData(0) //需要使用多少个可币分
    val voucherUseAmount = MutableLiveData(0) //需要使用券上多少金额
    val actualAmount = MutableLiveData<String>(null) //实际支付金额
    val actualCreditAmount = MutableLiveData(0) //实际使用多少个积分
    val creditKouAmount = MutableLiveData(0) //积分抵扣金额
    val combineDiscountAmount = MutableLiveData(0) //加购位立减少金额
    val isRemoteCalFinished = MutableLiveData<Boolean>(null)  //网络计算结果是否结束
    var currentChannelPromotionDiscountAmount = MutableLiveData(0) //当前支付渠道的优惠金额
    var calculateResponseV2 = MutableLiveData<CalculateResponseV2?>(null) //无活动金额和有活动支付方式金额

    val limitMap = MutableLiveData<MutableMap<String, String>>(mutableMapOf()) // 优惠立减后的限额Map

    /**
     * 支付订单号
     */
    val payRequestId = MutableLiveData<String>()
    /**
     * 纯签约合同编号
     */
    val contractCode = MutableLiveData<String>()


    val creditSwitchBoxStatus = MutableLiveData(false) //积分switch开关状态

    /**
     * 是否需要执行实付金额动画进行刷新
     * true 需要动画进行刷新
     */
    val needAmountAnim = MutableLiveData(false)

    /**
     * 缓存上次的实付金额的值用于下次动画刷新 缓存和收银台生命周期一致的容器中，避免多收银台问题
     */
    val lastAmountValue = MutableLiveData("0")


    /**
     * 刷新虚拟资产 外销使用
     * 场景：
     * 1、渠道登录成功后需要刷新，刷新虚拟资产
     * 2、收银台充值场景
     *
     */
    val needUpdateAssetData = MutableLiveData<Boolean?>(null)

    /**
     * 区分是否是内部流程还是外部流程，内部流程 可币充值不对外发送广播 外销使用
     */
    val payActionType = MutableLiveData<String>(null)


    /**
     * 当前可币券
     */
    var currentVoucher = MutableLiveData<Voucher?>()

    /**
     * 当前可币
     */
    var currentCoCoin = MutableLiveData<Float?>()

    /**
     * 当前积分
     */
    var currentCredit = MutableLiveData<Float?>()

    /**
     * 虚拟资产勾选状态
     */
    var deductionStatus = MutableLiveData(VirtualAssetDeductionStatus(isUseMaxCoinDeduction = true, isUseMaxCreditDeduction = true))

    /**
     * 设置默认渠道完毕
     */
    val setDefeatChannel = MutableLiveData<Boolean>()

    private fun setResults(response: CalculateUseCase.CalculateResponse?) {
        response?.let {

            response.calculateResponseV2.value?.promotionAmt?.forEach { promotion ->
                val paymentCode = promotion.paymentCode
                val actualAmount = promotion.actualAmount

                val newMap = mutableMapOf<String, String>()

                if (paymentCode != null && actualAmount != null) {
                    newMap.put(paymentCode, actualAmount)
                }
                limitMap.value = newMap
            }
            val currentMap = limitMap.value ?: mutableMapOf()
            response.calculateResponseV2.value?.tradeAmt?.actualAmount?.let {
                currentMap.put(NO_PROMOTION_PAYMENTCODE, it)
                limitMap.value = currentMap
            }

            kebiAmount.value = response.kebiAmount.value
            voucherUseAmount.value = response.voucherUseAmount.value
            actualAmount.value = response.actualAmount.value?.replace(",", ".")
            actualCreditAmount.value = response.actualCreditAmount.value
            creditKouAmount.value = response.creditKouAmount.value
            combineDiscountAmount.value = response.combineDiscountAmount.value
            isRemoteCalFinished.value = response.isRemoteCalFinished.value
            calculateResponseV2.value = response.calculateResponseV2.value
            currentChannelPromotionDiscountAmount.value = response.currentChannelPromotionDiscountAmount.value
            isEnough.value = response.isEnough.value
        }
    }

    fun setResults(response: CalculateUseCase.CalculateResults?) {
        response?.let {

            response.calculateResponseV2?.promotionAmt?.forEach { promotion ->
                val paymentCode = promotion.paymentCode
                val actualAmount = promotion.actualAmount

                val newMap = mutableMapOf<String, String>()

                if (paymentCode != null && actualAmount != null) {
                    newMap.put(paymentCode, actualAmount)
                }
                limitMap.value = newMap
            }
            val currentMap = limitMap.value ?: mutableMapOf()
            response.calculateResponseV2?.tradeAmt?.actualAmount?.let {
                currentMap.put(NO_PROMOTION_PAYMENTCODE, it)
                limitMap.value = currentMap
            }

            kebiAmount.value = response.kebiAmount
            voucherUseAmount.value = response.voucherUseAmount
            actualAmount.value = response.actualAmount?.replace(",", ".")
            // 可币充值时自定义输入会改变mAmount，使用payRequest的mAmount才是最新值
            actualAmount.value = BigDecimal(payRequest.value?.mAmount.toString()).times(BigDecimal(100)).toString()
            actualCreditAmount.value = response.actualCreditAmount
            creditKouAmount.value = response.creditKouAmount
            combineDiscountAmount.value = response.combineDiscountAmount
            isRemoteCalFinished.value = response.isRemoteCalFinished?:null
            calculateResponseV2.value = response.calculateResponseV2
            currentChannelPromotionDiscountAmount.value = response.currentChannelPromotionDiscountAmount
            isEnough.value = response.isEnough?:null
        }
    }

    fun localCalculate(
        assets: V2Assets?,
    ) {
        val localResult = CalculateUseCase().localCalculate(
            assets,
            payRequest.value!!,
            currentVoucher.value,
            creditSwitchBoxStatus.value!!,
            attachCheckBox.value!!,
            combineOrderInfo.value,
            currentCoCoin.value,
            currentCredit.value
        )
        setResults(localResult)
    }

    fun calculate(
        isAcrossSceen: Boolean,
        assets: V2Assets?,
    ) {
        //非预下单暂不支持平台立减，可以先加载本地计算，预下单的本地计算移到金额计算接口报错，防止网络异常没有金额展示
        // 待非预下单支持平台立减后移除此逻辑，不做差异化处理
        if (TextUtils.isEmpty(payRequest.value?.prePayToken ?: "")) {
            localCalculate(assets)
        }
        val serverResult = CalculateUseCase().serverCalculation(
            isAcrossSceen,
            assets,
            combineOrderInfo.value,
            payRequest.value!!,
            currentVoucher.value,
            currentChannel.value,
            creditSwitchBoxStatus.value!!,
            attachCheckBox.value!!,
            currentCoCoin.value,
            currentCredit.value
        )
        serverResult.observeForever(object :
            Observer<Resource<CalculateUseCase.CalculateResponse>> {
            override fun onChanged(t: Resource<CalculateUseCase.CalculateResponse>?) {
                when (t?.status) {
                    Status.SUCCESS -> {
                        setResults(t.data)
                        serverResult.removeObserver(this)
                    }

                    Status.ERROR -> {
                        serverResult.removeObserver(this)
                        if (!TextUtils.isEmpty(payRequest.value?.prePayToken ?: "")) {
                            localCalculate(assets)
                        }
                    }

                    else -> {
                        // nothing
                    }
                }
            }
        })
    }

    /**
     * 判断是否有券
     */
    fun isVoucherBuyPlace(): Boolean {
        return combineOrderInfo.value?.voucherInfos?.isNotEmpty() == true
    }

    /**
     * 架构位样式判断
     * 样式4："buyplace_with_voucher_v3"新版优惠券加购位
     * 样式3："buyplace_with_voucher_v2"新版优惠券加购位
     * 样式2："discount"立减加购位
     * 样式1："voucher"优惠券加购位
     */
    fun isVipBuyPlace(): Int {
        return when (combineOrderInfo.value?.displayTemplate) {
            BuyPlaceDisplayType.VOUCHER.type -> buyPlaceOne
            BuyPlaceDisplayType.DISCOUNT.type -> buyPlaceTwo
            BuyPlaceDisplayType.VOUCHER_V2.type, BuyPlaceDisplayType.VOUCHER_V3.type -> buyPlaceThree
            else -> 0
        }
    }

    fun dispatchPay(
        activity: Activity,
        viewLifecycleOwner: LifecycleOwner,
        defaultPayType: String?,
        isPayLoading: MutableLiveData<Boolean>?=null,
    ): String {
        val screenType =
            if (activity.resources?.configuration?.orientation == Configuration.ORIENTATION_LANDSCAPE && DeviceInfoHelper.isNeedLandScape(
                    AppRuntime.getAppContext()
                )
            ) {
                ScreenType.ACROSSSCREEN.type
            } else {
                ScreenType.FULLSCREEN.type
            }

        return PayHelper(
            payRequest.value!!,
            activity,
            viewLifecycleOwner,
            screenType,
            attachCheckBox.value!!,
            currentChannel.value,
            currentVoucher.value,
            combineOrderInfo.value,
            actualAmount.value?: "0",
            voucherUseAmount.value!!,
            actualCreditAmount.value!!,
            isEnough.value ?: false,
            creditSwitchBoxStatus.value!!,
            isVoucherBuyPlace(),
            assets.value?.data,
            currentCoCoin.value,
            currentCredit.value,
            payRequestId,
            contractCode,
            defaultPayType,
            isPayLoading
        ).dispatchPay(null)
    }

//    fun isCurrentTradeCenter(activity: Activity, parentFragment: Fragment?): Boolean {
//        val orientation = activity.resources?.configuration?.orientation
//        return if (DeviceInfoHelper.isNeedLandScape(
//                AppRuntime.getAppContext()
//            )
//        ) {
//            when (orientation) {
//                Configuration.ORIENTATION_LANDSCAPE -> {
//                    parentFragment is AcrossTradeCenterFragment
//                }
//
//                Configuration.ORIENTATION_PORTRAIT -> {
//                    parentFragment !is AcrossTradeCenterFragment
//                }
//
//                else -> {
//                    true
//                }
//            }
//        } else {
//            true
//        }
//    }

    fun setAmount(
        discountAmount: Int,
        calculateResponse: CalculateResponse
    ) {
        kebiAmount.value = calculateResponse.cocoinDeductAmount
        voucherUseAmount.value = calculateResponse.voucherDeductAmount
        actualAmount.value = calculateResponse.actualAmount?.replace(",", ".")
        actualCreditAmount.value = calculateResponse.actualCreditCount
        creditKouAmount.value = calculateResponse.creditDeductAmount
        combineDiscountAmount.value = calculateResponse.combineDiscountAmount
        currentChannelPromotionDiscountAmount.value = discountAmount
    }

    /**
     * 判断是否为待开通快捷支付
     */
    fun isOpenQuickPay(channel: V2Channel?): Boolean{
        return channel?.paymentType == PAYMENT_TYPE_FREEPWD_PAY
                && channel.freePwdInfo?.isSigned == BizResultType.NO.value
    }
}