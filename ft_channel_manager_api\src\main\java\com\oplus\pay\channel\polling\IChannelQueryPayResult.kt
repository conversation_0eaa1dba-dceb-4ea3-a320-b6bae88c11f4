package com.oplus.pay.channel.polling

import androidx.lifecycle.DefaultLifecycleObserver

/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright (c) 2020 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
interface IChannelQueryPayResult : DefaultLifecycleObserver {


    fun startQueryChannelResult(
        pollingEndAndNotifyPaySuccessResultFunc: () -> Unit,
        pollingEndAndNotifyPayErrorResultFunc: () -> Unit,
        countryCode: String?,
        prePayToken: String?,
        payReqId: String?,
        contractCode: String?,
    )

}