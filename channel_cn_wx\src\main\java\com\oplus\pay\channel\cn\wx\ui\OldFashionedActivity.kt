package com.oplus.pay.channel.cn.wx.ui

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.Observer
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.basic.Status
import com.oplus.pay.basic.util.serialize.GSON
import com.oplus.pay.channel.cn.statistic.StaticHelper
import com.oplus.pay.channel.cn.wx.PAYMENT_CODE_WX_PAY
import com.oplus.pay.channel.model.request.OpenChannelParams
import com.oplus.pay.monitor.api.d
import com.oplus.pay.monitor.api.model.BizNode
import com.oplus.pay.monitor.api.model.BizResult
import com.oplus.pay.monitor.api.model.ChannelResultStatusCodes
import com.oplus.pay.order.PayCenterHelper
import com.oplus.pay.order.model.request.OldFashionedOrderInfo
import com.oplus.pay.outcomes.OldFashionedParam
import com.oplus.pay.outcomes.OutcomesHelper
import com.oplus.pay.outcomes.OutcomesParam
import com.oplus.pay.outcomes.model.OldFashionedSignResponse
import com.oplus.pay.ui.BaseActivity
import com.oplus.pay.ui.recomBine
import com.oplus.pay.ui.util.LoadingDialogHelper
import org.json.JSONObject

/**
 * <p>Title: OldFashionedActivity</p>
 * <p>Description: OldFashionedActivity</p>
 * <p>Copyright (c) 2021 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR> by 80230486 on 2021/8/14.
 *
 */
private const val TAG = "QMFActivity"
class OldFashionedActivity : BaseActivity() {
    companion object {
        const val EXTRA_OPEN_CHANNEL_PARAMS = "openChannelParams"

        fun launch(context: Activity, openChannelParams: OpenChannelParams) {
            context.startActivity(Intent(context, OldFashionedActivity::class.java).apply {
                putExtra(
                    EXTRA_OPEN_CHANNEL_PARAMS,
                    openChannelParams.toJson()
                )
                    .recomBine(openChannelParams.bizExtra.mPayId ?: "")
            })
        }
    }

    private val openChannelParams: OpenChannelParams? by lazy {
        val json = intent.getStringExtra(
            EXTRA_OPEN_CHANNEL_PARAMS
        )
        OpenChannelParams.fromJson(json!!)
    }

    override fun onStart() {
        super.onStart()
        hideSystemUI()
        showLoading()
    }

    private fun showLoading() {
        LoadingDialogHelper.showLoadingDialog(this,true) {
            finish()
        }
    }

    override fun onStop() {
        super.onStop()
        LoadingDialogHelper.dismissLoadingDialog()
    }

    override fun onResume() {
        super.onResume()
        // 老版本的云服务签约，微信签约需要主动查询
        queryOldFashionedSignResult()
    }

    /**
     * 查询签约结果，成功后再调用扣费接口扣费
     */
    private fun queryOldFashionedSignResult() {
        openChannelParams?.apply {

            val oldFashionedParam = OldFashionedParam(
                paymentCode,
                appPackage,
                bizExt = bizExt
            )
            val result = OutcomesHelper.queryOldFashionedSignResult(oldFashionedParam)

            result.observe(this@OldFashionedActivity, Observer {
                it?.let {
                    when (it.status) {
                        Status.ERROR -> {
                            val buildCommonMap = StaticHelper.buildCommonMap(
                                processToken = openChannelParams?.bizExt?.processToken ?: "",
                                order = openChannelParams?.bizExt?.partnerOrder ?: "",
                                prePayToken = openChannelParams?.bizExt?.prePayToken ?: "",
                                bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                                payBizScene = StaticHelper.getPayBizScene(openChannelParams?.bizExt?.contractType),
                                bizResult = BizResult.WARN.value,
                                bizErrorMsg = "${it.code}-${it.message}",
                                bizCode = ChannelResultStatusCodes.CODE_04_000_0002.statusCode,
                                payType = StaticHelper.getPayType(openChannelParams),
                                outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                                    put("partnerCode",openChannelParams?.bizExt?.partnerCode ?: "")
                                })
                            )
                            PayLogUtil.d(TAG, buildCommonMap)
                            StaticHelper.eventIdChannelPayEnd(buildCommonMap)
                            OutcomesHelper.notifyUnknown(
                                OutcomesParam(
                                    paymentCode,
                                    appPackage,
                                    bizExt = bizExt
                                )
                            )
                            notifyFinish()
                        }
                        Status.SUCCESS -> {
                            it.data?.apply {
                                oldFashionedPay(this)
                            }
                        }
                        else -> {
                            // nothing
                        }
                    }
                }

            })
        }
    }

    private fun oldFashionedPay(signResponse: OldFashionedSignResponse) {
        if ("0000" == signResponse.code) {
            try {
                val contractId = JSONObject(signResponse.message).optString("CONTRACT_ID")
                openChannelParams?.apply {
                    val oldFashionedOrderInfo = OldFashionedOrderInfo(
                        amount = bizExt.actualAmount,
                        productName = bizExt.productName!!,
                        productDesc = null,
                        contractId = contractId,
                        appPackage = appPackage,
                        appVersion = null,
                        notifyUrl = bizExtra.notifyUrl!!,
                        payType = PAYMENT_CODE_WX_PAY,
                        bizExt = bizExtra
                    )

                    val result = PayCenterHelper.oldFashionedAvoidPay(
                        this@OldFashionedActivity,
                        oldFashionedOrderInfo
                    )

                    result.observe(this@OldFashionedActivity, Observer {
                        it?.let {
                            when (it.status) {
                                Status.ERROR -> {
                                    OutcomesHelper.notifyUnknown(
                                        OutcomesParam(
                                            paymentCode,
                                            appPackage,
                                            bizExt = bizExt
                                        )
                                    )
                                    val buildCommonMap = StaticHelper.buildCommonMap(
                                        processToken = openChannelParams?.bizExt?.processToken ?: "",
                                        order = openChannelParams?.bizExt?.partnerOrder ?: "",
                                        prePayToken = openChannelParams?.bizExt?.prePayToken ?: "",
                                        bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                                        payBizScene = StaticHelper.getPayBizScene(openChannelParams?.bizExt?.contractType),
                                        bizResult = BizResult.WARN.value,
                                        bizErrorMsg = "${it.code}-${it.message}",
                                        bizCode = ChannelResultStatusCodes.CODE_04_000_0002.statusCode,
                                        payType = StaticHelper.getPayType(openChannelParams),
                                        outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                                            put("partnerCode",openChannelParams?.bizExt?.partnerCode ?: "")
                                            put("contractId",contractId)
                                            put("signResponseCode",signResponse.code)
                                        })
                                    )
                                    PayLogUtil.d(TAG, buildCommonMap)
                                    StaticHelper.eventIdChannelPayEnd(buildCommonMap)
                                    notifyFinish()
                                }
                                Status.SUCCESS -> {
                                    it.data?.apply {
                                        val buildCommonMap = StaticHelper.buildCommonMap(
                                            processToken = openChannelParams?.bizExt?.processToken ?: "",
                                            order = openChannelParams?.bizExt?.partnerOrder ?: "",
                                            prePayToken = openChannelParams?.bizExt?.prePayToken ?: "",
                                            bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                                            payBizScene = StaticHelper.getPayBizScene(openChannelParams?.bizExt?.contractType),
                                            bizResult = BizResult.SUCCESS.value,
                                            bizErrorMsg = "${it.code}-${it.message}",
                                            bizCode = ChannelResultStatusCodes.CODE_04_000_0013.statusCode,
                                            payType = StaticHelper.getPayType(openChannelParams),
                                            outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                                                put("partnerCode",openChannelParams?.bizExt?.partnerCode ?: "")
                                                put("signResponseCode",signResponse.code)
                                            })
                                        )
                                        PayLogUtil.d(TAG, buildCommonMap)
                                        StaticHelper.eventIdChannelPayEnd(buildCommonMap)
                                        OutcomesHelper.notifySuccess(
                                            OutcomesParam(
                                                paymentCode,
                                                appPackage,
                                                bizExt = bizExt
                                            )
                                        )

                                        notifyFinish()
                                    }
                                }
                                else -> {
                                    // nothing
                                }
                            }
                        }
                    })
                }
            } catch (e: Exception) {
                notifyFinish()
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = openChannelParams?.bizExt?.processToken ?: "",
                    order = openChannelParams?.bizExt?.partnerOrder ?: "",
                    prePayToken = openChannelParams?.bizExt?.prePayToken ?: "",
                    bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                    payBizScene = StaticHelper.getPayBizScene(openChannelParams?.bizExt?.contractType),
                    bizResult = BizResult.ERROR.value,
                    bizErrorMsg = "${e.message}",
                    bizCode = ChannelResultStatusCodes.CODE_04_000_0007.statusCode,
                    payType = StaticHelper.getPayType(openChannelParams),
                    outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                        put("partnerCode",openChannelParams?.bizExt?.partnerCode ?: "")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdChannelPayEnd(buildCommonMap)
            }
        } else {
            val buildCommonMap = StaticHelper.buildCommonMap(
                processToken = openChannelParams?.bizExt?.processToken ?: "",
                order = openChannelParams?.bizExt?.partnerOrder ?: "",
                prePayToken = openChannelParams?.bizExt?.prePayToken ?: "",
                bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                payBizScene = StaticHelper.getPayBizScene(openChannelParams?.bizExt?.contractType),
                bizResult = BizResult.WARN.value,
                bizErrorMsg = "${signResponse.code}-${signResponse.message}",
                bizCode = ChannelResultStatusCodes.CODE_04_000_0002.statusCode,
                payType = StaticHelper.getPayType(openChannelParams),
                outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                    put("partnerCode",openChannelParams?.bizExt?.partnerCode ?: "")
                    put("signResponseCode",signResponse.code)
                })
            )
            PayLogUtil.d(TAG, buildCommonMap)
            StaticHelper.eventIdChannelPayEnd(buildCommonMap)
            notifyFinish()
        }
    }

    private fun notifyFinish() {
        LoadingDialogHelper.dismissLoadingDialog()
        finish()
    }
}